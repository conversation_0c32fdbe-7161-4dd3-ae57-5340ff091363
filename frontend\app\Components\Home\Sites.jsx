import React, { useEffect, useState } from 'react';
import CategorySection from '../Shared/CategorySection';
import { useRouter } from 'expo-router';
import { showToast } from '../../../utils/showToast';
import { fetchSites } from '../../../api/sites/GetSites';

export default function Sites() {
    const router = useRouter();
    const [sites, setSites] = useState([]);

    useEffect(() => {
        const loadSites = async () => {
            try {
                const data = await fetchSites();
                setSites(data);
            } catch (error) {
                showToast(
                    'error',
                    'Error',
                    'Failed to load listings. Please try again.'
                );
            }
        };
        loadSites();
    }, []);

    const handleItemPress = (item) => {
        if (!item._id) {
            showToast(
                'error',
                'Error',
                'Invalid land or site scout information.'
            );
            return;
        }
        router.push({
            pathname: '/Sites/SiteList/SiteDetailsScreen',
            params: { siteId: item._id },
        });
    };

    const handleInterestedPress = (item) => {
        if (!item.id) {
            showToast(
                'error',
                'Error',
                'Invalid land or site scout information.'
            );
            return;
        }
        router.push({
            pathname: '/Chat/LandDiscussion',
            params: { landId: item.id, brokerId: item.broker.id },
        });
    };

    return (
        <CategorySection
            title="Lands Near You"
            data={sites.map((site) => ({
                image: site.thumbnail?.imageURL,
                ...site,
                onInterestedPress: () => handleInterestedPress(site),
                accessibilityLabel: `View details for ${site.title} at ${site.location}`,
            }))}
            onItemPress={handleItemPress}
            viewAllRoute="/Sites/SiteList/SiteList"
        />
    );
}
