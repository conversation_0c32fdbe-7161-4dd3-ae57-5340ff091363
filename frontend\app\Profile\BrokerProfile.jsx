import React, { useContext, useState } from 'react';
import {
    View,
    Text,
    Image,
    TouchableOpacity,
    StyleSheet,
    ScrollView,
    SafeAreaView,
    ActivityIndicator,
    Dimensions,
    StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams } from 'expo-router';
import { useQuery } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import { fetchBrokerProfileById } from '../../api/broker/brokerApi';
import { fetchBrokerProfile } from '../../api/broker/brokerApi';
import { fetchUserProfile } from '../../api/user/userApi';
import BackButton from '../Components/Shared/BackButton';
import { showToast } from '../../utils/showToast';

const { width } = Dimensions.get('window');

export default function BrokerProfile() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const { brokerId } = useLocalSearchParams();
    const [isHiring, setIsHiring] = useState(false);

    // Get current user profile to check if viewing own profile
    const { data: currentUser, isLoading: isUserLoading } = useQuery({
        queryKey: ['userProfile'],
        queryFn: fetchUserProfile,
    });

    // Get current user's broker profile if they are a contractor
    const { data: currentBrokerProfile, isLoading: isCurrentBrokerLoading } =
        useQuery({
            queryKey: ['brokerProfile'],
            queryFn: fetchBrokerProfile,
            enabled: currentUser?.role === 'broker',
            retry: false,
        });
    const isOwnProfile =
        currentUser?.role === 'broker' &&
        currentBrokerProfile?.brokerId === brokerId;

    const {
        data: broker,
        isLoading,
        isError,
        error,
        refetch,
    } = useQuery({
        queryKey: ['brokerProfile', brokerId],
        queryFn: () => fetchBrokerProfileById(brokerId),
        enabled: !!brokerId,
    });

    const handleHire = async () => {
        setIsHiring(true);
        try {
            showToast(
                'info',
                'Coming Soon',
                'Hire functionality will be available soon!'
            );
        } finally {
            setIsHiring(false);
        }
    };

    const handleMessage = () => {
        showToast(
            'info',
            'Coming Soon',
            'Message functionality will be available soon!'
        );
    };

    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;

        for (let i = 0; i < fullStars; i++) {
            stars.push(
                <Ionicons key={i} name="star" size={14} color="#FFD700" />
            );
        }

        if (hasHalfStar) {
            stars.push(
                <Ionicons
                    key="half"
                    name="star-half"
                    size={14}
                    color="#FFD700"
                />
            );
        }

        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            stars.push(
                <Ionicons
                    key={`empty-${i}`}
                    name="star-outline"
                    size={14}
                    color="#FFD700"
                />
            );
        }

        return stars;
    };

    if (isLoading) {
        return (
            <SafeAreaView
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={theme.PRIMARY} />
                    <Text
                        style={[
                            styles.loadingText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Loading profile...
                    </Text>
                </View>
            </SafeAreaView>
        );
    }

    if (isError) {
        return (
            <SafeAreaView
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <View style={styles.errorContainer}>
                    <Ionicons
                        name="alert-circle"
                        size={48}
                        color={theme.ERROR}
                    />
                    <Text
                        style={[
                            styles.errorText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Failed to load broker profile
                    </Text>
                    <Text
                        style={[
                            styles.errorSubtext,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {error?.message || 'Please try again later'}
                    </Text>
                    <TouchableOpacity
                        style={[
                            styles.retryButton,
                            { backgroundColor: theme.PRIMARY },
                        ]}
                        onPress={refetch}
                        accessibilityLabel="Retry loading profile"
                    >
                        <Text style={styles.retryButtonText}>Retry</Text>
                    </TouchableOpacity>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                backgroundColor={theme.PRIMARY}
            />
            <ScrollView showsVerticalScrollIndicator={false}>
                <View style={styles.backgroundImageContainer}>
                    <Image
                        source={
                            isDarkMode
                                ? require('../../assets/images/background_dark.png')
                                : require('../../assets/images/background_light.png')
                        }
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                </View>

                <View style={styles.header}>
                    <View style={styles.topBar}>
                        <BackButton color={theme.WHITE} />
                    </View>
                    <View style={styles.profileContainer}>
                        <View style={styles.profileHeader}>
                            <View
                                style={{
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    justifyContent: 'flex-start',
                                }}
                            >
                                <View style={styles.avatarContainer}>
                                    {broker?.image ? (
                                        <Image
                                            source={{ uri: broker.image }}
                                            style={[
                                                styles.avatar,
                                                { borderColor: theme.PRIMARY },
                                            ]}
                                            resizeMode="cover"
                                            accessibilityLabel={`${broker?.name}'s profile picture`}
                                        />
                                    ) : (
                                        <View
                                            style={[
                                                styles.avatarPlaceholder,
                                                {
                                                    backgroundColor:
                                                        theme.INPUT_BACKGROUND,
                                                    borderColor: theme.PRIMARY,
                                                },
                                            ]}
                                        >
                                            <Ionicons
                                                name="person"
                                                size={50}
                                                color={theme.TEXT_SECONDARY}
                                            />
                                        </View>
                                    )}
                                    <View
                                        style={[
                                            styles.availabilityIndicator,
                                            {
                                                backgroundColor:
                                                    broker?.isAvailable
                                                        ? theme.SUCCESS
                                                        : theme.ERROR,
                                                borderColor: theme.WHITE,
                                            },
                                        ]}
                                    >
                                        <View style={styles.availabilityDot} />
                                    </View>
                                </View>
                                <View
                                    style={{
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    }}
                                >
                                    <Text
                                        style={[
                                            styles.availability,
                                            {
                                                color: broker?.isAvailable
                                                    ? '#4CAF50'
                                                    : '#F44336',
                                            },
                                        ]}
                                    >
                                        {broker?.isAvailable
                                            ? 'Available'
                                            : 'Not Available'}
                                    </Text>
                                </View>
                            </View>
                            <View
                                style={{
                                    flexDirection: 'column',
                                    flex: 1,
                                    alignItems: 'center',
                                    justifyContent: 'flex-start',
                                }}
                            >
                                <Text
                                    style={[
                                        styles.name,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    {broker?.name}
                                </Text>
                                <View
                                    style={[
                                        styles.statsContainer,
                                        {
                                            shadowColor: theme.PRIMARY,
                                        },
                                    ]}
                                >
                                    <View style={styles.statItem}>
                                        <Text
                                            style={[
                                                styles.statNumber,
                                                { color: theme.PRIMARY },
                                            ]}
                                        >
                                            {broker?.experience || 0}
                                        </Text>
                                        <Text
                                            style={[
                                                styles.statLabel,
                                                { color: theme.TEXT_SECONDARY },
                                            ]}
                                        >
                                            Years
                                        </Text>
                                    </View>
                                    <View style={styles.statItem}>
                                        <View style={styles.ratingContainer}>
                                            <View style={styles.starsContainer}>
                                                {renderStars(
                                                    broker?.ratings || 0
                                                )}
                                            </View>
                                            <Text
                                                style={[
                                                    styles.ratingText,
                                                    { color: theme.PRIMARY },
                                                ]}
                                            >
                                                {(broker?.ratings || 0).toFixed(
                                                    1
                                                )}
                                            </Text>
                                        </View>
                                        <Text
                                            style={[
                                                styles.statLabel,
                                                { color: theme.TEXT_SECONDARY },
                                            ]}
                                        >
                                            Rating
                                        </Text>
                                    </View>
                                </View>
                            </View>
                        </View>
                        {!isOwnProfile && (
                            <View
                                style={{
                                    flexDirection: 'row',
                                    gap: 10,
                                    marginHorizontal: 2,
                                }}
                            >
                                <TouchableOpacity
                                    style={[
                                        styles.hireButton,
                                        { backgroundColor: theme.PRIMARY },
                                    ]}
                                    onPress={handleHire}
                                    activeOpacity={0.8}
                                    disabled={isHiring}
                                    accessibilityLabel="Hire broker"
                                >
                                    <LinearGradient
                                        colors={[
                                            theme.PRIMARY,
                                            theme.SECONDARY,
                                        ]}
                                        start={{ x: 0, y: 0 }}
                                        end={{ x: 1.2, y: 1 }}
                                        style={styles.hireButtonGradient}
                                    >
                                        {isHiring ? (
                                            <ActivityIndicator
                                                size="small"
                                                color={theme.WHITE}
                                            />
                                        ) : (
                                            <>
                                                <Ionicons
                                                    name="briefcase"
                                                    size={20}
                                                    color={theme.WHITE}
                                                />
                                                <Text
                                                    style={[
                                                        styles.hireButtonText,
                                                        { color: theme.WHITE },
                                                    ]}
                                                >
                                                    Hire
                                                </Text>
                                            </>
                                        )}
                                    </LinearGradient>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={[
                                        styles.hireButton,
                                        { backgroundColor: theme.WHITE },
                                    ]}
                                    onPress={handleMessage}
                                    activeOpacity={0.8}
                                    disabled={isHiring}
                                    accessibilityLabel="Message broker"
                                >
                                    <LinearGradient
                                        colors={[theme.WHITE, theme.GRAY_LIGHT]}
                                        start={{ x: 0, y: 0 }}
                                        end={{ x: 1.2, y: 1 }}
                                        style={styles.hireButtonGradient}
                                    >
                                        {isHiring ? (
                                            <ActivityIndicator
                                                size="small"
                                                color={theme.WHITE}
                                            />
                                        ) : (
                                            <>
                                                <Ionicons
                                                    name="chatbubbles-outline"
                                                    size={20}
                                                    color={theme.PRIMARY}
                                                />
                                                <Text
                                                    style={[
                                                        styles.hireButtonText,
                                                        {
                                                            color: theme.PRIMARY,
                                                        },
                                                    ]}
                                                >
                                                    Message
                                                </Text>
                                            </>
                                        )}
                                    </LinearGradient>
                                </TouchableOpacity>
                            </View>
                        )}
                    </View>

                    <View
                        style={[
                            styles.section,
                            { backgroundColor: theme.CARD },
                        ]}
                    >
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            Service Areas
                        </Text>
                        <View style={styles.serviceAreasContainer}>
                            {broker?.serviceAreas &&
                            broker.serviceAreas.length > 0 ? (
                                broker.serviceAreas.map((area, index) => (
                                    <View
                                        key={index}
                                        style={[
                                            styles.serviceAreaTag,
                                            {
                                                backgroundColor:
                                                    theme.PRIMARY + '20',
                                                borderColor: theme.PRIMARY,
                                            },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.serviceAreaText,
                                                { color: theme.PRIMARY },
                                            ]}
                                        >
                                            {area}
                                        </Text>
                                    </View>
                                ))
                            ) : (
                                <Text
                                    style={[
                                        styles.noDataText,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    No service areas specified
                                </Text>
                            )}
                        </View>
                    </View>
                </View>
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    backgroundImageContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: width * 0.65,
        opacity: 0.9,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    header: {
        alignItems: 'center',
        paddingBottom: 10,
    },
    topBar: {
        width: '100%',
        paddingHorizontal: 10,
        alignItems: 'flex-start',
    },
    profileContainer: {
        alignItems: 'center',
        width: '100%',
        paddingHorizontal: 10,
        marginTop: 2,
    },
    profileHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        marginBottom: 4,
    },
    avatarContainer: {
        position: 'relative',
        marginRight: 10,
    },
    avatar: {
        width: 90,
        height: 90,
        borderRadius: 45,
        borderWidth: 3,
    },
    avatarPlaceholder: {
        width: 90,
        height: 90,
        borderRadius: 45,
        borderWidth: 3,
        alignItems: 'center',
        justifyContent: 'center',
    },
    availabilityIndicator: {
        position: 'absolute',
        bottom: 8,
        right: 8,
        width: 12,
        height: 12,
        borderRadius: 6,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
    },
    availabilityDot: {
        width: 4,
        height: 4,
    },
    name: {
        fontSize: 20,
        fontWeight: '700',
        marginBottom: 4,
    },
    availability: {
        fontSize: 14,
        fontWeight: '500',
        marginBottom: 12,
        textAlign: 'center',
    },
    statsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
        borderRadius: 10,
        paddingHorizontal: 10,
        flex: 1,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
    },
    statItem: {
        flex: 1,
        alignItems: 'center',
        marginHorizontal: 10,
    },
    statNumber: {
        fontSize: 16,
        fontWeight: '700',
        marginBottom: 2,
    },
    statLabel: {
        fontSize: 11,
        textAlign: 'center',
        opacity: 0.8,
    },
    ratingContainer: {
        alignItems: 'center',
        marginBottom: 2,
    },
    starsContainer: {
        flexDirection: 'row',
        marginBottom: 2,
    },
    ratingText: {
        fontSize: 14,
        fontWeight: '600',
    },
    hireButton: {
        borderRadius: 10,
        overflow: 'hidden',
        width: '48%',
        marginBottom: 6,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 4.65,
        elevation: 8,
    },
    hireButtonGradient: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 12,
        paddingHorizontal: 10,
    },
    hireButtonText: {
        fontSize: 14,
        fontWeight: '700',
        marginLeft: 10,
    },
    section: {
        width: '90%',
        marginBottom: 20,
        marginTop: 10,
        borderRadius: 10,
        padding: 16,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    sectionTitle: {
        fontSize: 12,
        fontWeight: '700',
        marginBottom: 8,
    },
    serviceAreasContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        gap: 8,
    },
    serviceAreaTag: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        borderWidth: 1,
    },
    serviceAreaText: {
        fontSize: 14,
        fontWeight: '500',
    },
    noDataText: {
        fontSize: 14,
        fontStyle: 'italic',
        opacity: 0.8,
        textAlign: 'center',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
        fontWeight: '500',
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 32,
    },
    errorText: {
        fontSize: 18,
        fontWeight: '600',
        marginTop: 16,
        textAlign: 'center',
    },
    errorSubtext: {
        fontSize: 14,
        marginTop: 8,
        textAlign: 'center',
        opacity: 0.8,
    },
    retryButton: {
        marginTop: 16,
        borderRadius: 8,
        paddingVertical: 12,
        paddingHorizontal: 24,
    },
    retryButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
});
