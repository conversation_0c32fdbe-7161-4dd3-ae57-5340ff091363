"use client";

import React from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { QUICK_ACCESS_ITEMS } from "@/lib/constants";

// Icon mapping for quick access items
const IconMap = {
  "home-map-marker": "🏠",
  engineering: "👷",
  "person-search": "🔍",
  map: "🗺️",
  "chatbubbles-outline": "💬",
  "support-agent": "🎧",
};

export default function HomePage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-primary/10 via-background to-accent/20 py-20 sm:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Connect. Build. <span className="text-primary">Grow.</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              Your comprehensive platform for real estate transactions,
              connecting property buyers, site scouts, and contractors in one
              seamless ecosystem.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/properties">Explore Properties</Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/auth/register">Join as Professional</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Access Section */}
      <section className="py-16 sm:py-24">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Quick Access
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Everything you need to manage your real estate journey
            </p>
          </div>

          <div className="mx-auto mt-16 grid max-w-6xl grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {QUICK_ACCESS_ITEMS.map((item) => (
              <Card
                key={item.id}
                className="group cursor-pointer transition-all hover:shadow-card-hover"
              >
                <Link href={item.route}>
                  <CardHeader className="text-center">
                    <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10 text-3xl transition-colors group-hover:bg-primary/20">
                      {IconMap[item.icon as keyof typeof IconMap] || "📋"}
                    </div>
                    <CardTitle className="text-xl">{item.name}</CardTitle>
                    <CardDescription>{item.description}</CardDescription>
                  </CardHeader>
                </Link>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="bg-accent/30 py-16 sm:py-24">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Why Choose BUILD-CONNECT?
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Comprehensive solutions for all your real estate needs
            </p>
          </div>

          <div className="mx-auto mt-16 grid max-w-6xl grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            <div className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                🔒
              </div>
              <h3 className="text-lg font-semibold">Verified Professionals</h3>
              <p className="mt-2 text-muted-foreground">
                All brokers and contractors are thoroughly verified and rated by
                the community
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                🤖
              </div>
              <h3 className="text-lg font-semibold">AI-Powered Insights</h3>
              <p className="mt-2 text-muted-foreground">
                Get intelligent property valuations and document verification
                using advanced AI
              </p>
            </div>

            <div className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                💬
              </div>
              <h3 className="text-lg font-semibold">Real-time Communication</h3>
              <p className="mt-2 text-muted-foreground">
                Seamless chat system for instant communication between all
                parties
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 sm:py-24">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready to get started?
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Join thousands of satisfied users who trust BUILD-CONNECT for
              their real estate needs
            </p>
            <div className="mt-8 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/auth/register">Get Started Today</Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/about">Learn More</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
