import React, { useState, useContext } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    TextInput,
    Image,
    SafeAreaView,
    FlatList,
    ActivityIndicator,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';
import { useQuery } from '@tanstack/react-query';
import BackButton from '../Components/Shared/BackButton';

// Placeholder API function - replace with actual implementation
const fetchAllSites = async () => {
    // Placeholder data
    return [
        {
            id: 1,
            name: 'Beautiful Plot in Whitefield',
            location: 'Whitefield, Bangalore',
            plotArea: '2400',
            price: '1200000',
            status: 'available',
            thumbnail: null,
            owner: '<PERSON>',
        },
        {
            id: 2,
            name: 'Prime Location Site',
            location: 'Electronic City, Bangalore',
            plotArea: '3000',
            price: '1800000',
            status: 'sold',
            thumbnail: null,
            owner: '<PERSON>',
        },
    ];
};

const SiteList = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedStatus, setSelectedStatus] = useState('all');
    const [isSearchBarVisible, setSearchBarVisible] = useState(false);

    const {
        data: sites = [],
        isLoading,
        error,
        refetch,
    } = useQuery({
        queryKey: ['allSites'],
        queryFn: fetchAllSites,
    });

    const filteredSites = sites.filter((site) => {
        const matchesSearch =
            (site.name?.toLowerCase() || '').includes(
                searchQuery.toLowerCase()
            ) ||
            (site.location?.toLowerCase() || '').includes(
                searchQuery.toLowerCase()
            );

        const matchesStatus =
            selectedStatus === 'all' || site.status === selectedStatus;

        return matchesSearch && matchesStatus;
    });

    const renderSite = ({ item: site }) => (
        <TouchableOpacity
            style={[
                styles.siteCard,
                { backgroundColor: theme.CARD, shadowColor: theme.PRIMARY },
            ]}
            onPress={() =>
                router.push(`/Sites/SiteDetails?siteId=${site.id}`)
            }
        >
            <View style={styles.siteHeader}>
                <View style={[
                    styles.statusBadge,
                    { backgroundColor: site.status === 'available' ? theme.SUCCESS : theme.ERROR }
                ]}>
                    <Text style={[styles.statusText, { color: theme.WHITE }]}>
                        {site.status === 'available' ? 'Available' : 'Sold'}
                    </Text>
                </View>
                {site.thumbnail ? (
                    <Image
                        source={{ uri: site.thumbnail }}
                        style={styles.siteImage}
                    />
                ) : (
                    <View
                        style={[
                            styles.siteImage,
                            { backgroundColor: theme.GRAY_LIGHT + 'CC' },
                        ]}
                    >
                        <Ionicons
                            name="home"
                            size={50}
                            color={theme.TEXT_SECONDARY}
                        />
                    </View>
                )}
            </View>
            <View style={styles.siteInfo}>
                <Text style={[styles.siteName, { color: theme.TEXT_PRIMARY }]}>
                    {site.name}
                </Text>
                <Text style={[styles.siteLocation, { color: theme.TEXT_SECONDARY }]}>
                    📍 {site.location}
                </Text>
                <View style={styles.siteDetails}>
                    <Text style={[styles.siteArea, { color: theme.TEXT_PRIMARY }]}>
                        📐 {site.plotArea} sqft
                    </Text>
                    <Text style={[styles.sitePrice, { color: theme.PRIMARY }]}>
                        ₹{parseInt(site.price).toLocaleString()}
                    </Text>
                </View>
                <Text style={[styles.siteOwner, { color: theme.TEXT_SECONDARY }]}>
                    Owner: {site.owner}
                </Text>
            </View>
        </TouchableOpacity>
    );

    if (isLoading) {
        return (
            <View style={[styles.loadingContainer, { backgroundColor: theme.BACKGROUND }]}>
                <ActivityIndicator size="large" color={theme.PRIMARY} />
                <Text style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}>
                    Loading sites...
                </Text>
            </View>
        );
    }

    if (error) {
        return (
            <View style={[styles.errorContainer, { backgroundColor: theme.BACKGROUND }]}>
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    Failed to load sites. Please try again.
                </Text>
                <TouchableOpacity
                    style={[styles.retryButton, { backgroundColor: theme.PRIMARY }]}
                    onPress={refetch}
                >
                    <Text style={[styles.retryButtonText, { color: theme.WHITE }]}>
                        Retry
                    </Text>
                </TouchableOpacity>
            </View>
        );
    }

    return (
        <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <LinearGradient
                colors={[theme.GRADIENT_PRIMARY, theme.GRADIENT_SECONDARY]}
                style={styles.header}
            >
                <BackButton color={theme.WHITE} />
                <View style={styles.headerContent}>
                    <Text style={[styles.headerTitle, { color: theme.WHITE }]}>
                        All Sites
                    </Text>
                    <TouchableOpacity
                        onPress={() => setSearchBarVisible(!isSearchBarVisible)}
                        style={styles.searchIcon}
                    >
                        <Ionicons name="search" size={24} color={theme.WHITE} />
                    </TouchableOpacity>
                </View>
            </LinearGradient>

            {isSearchBarVisible && (
                <View style={[styles.searchContainer, { backgroundColor: theme.CARD }]}>
                    <TextInput
                        style={[
                            styles.searchInput,
                            {
                                backgroundColor: theme.INPUT_BACKGROUND,
                                color: theme.TEXT_PRIMARY,
                                borderColor: theme.INPUT_BORDER,
                            },
                        ]}
                        placeholder="Search sites..."
                        placeholderTextColor={theme.TEXT_SECONDARY}
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                    />
                </View>
            )}

            <View style={styles.filterContainer}>
                {['all', 'available', 'sold'].map((status) => (
                    <TouchableOpacity
                        key={status}
                        style={[
                            styles.filterButton,
                            selectedStatus === status && {
                                backgroundColor: theme.PRIMARY,
                            },
                            { borderColor: theme.PRIMARY },
                        ]}
                        onPress={() => setSelectedStatus(status)}
                    >
                        <Text
                            style={[
                                styles.filterButtonText,
                                {
                                    color:
                                        selectedStatus === status
                                            ? theme.WHITE
                                            : theme.PRIMARY,
                                },
                            ]}
                        >
                            {status.charAt(0).toUpperCase() + status.slice(1)}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>

            <FlatList
                data={filteredSites}
                renderItem={renderSite}
                keyExtractor={(item) => item.id.toString()}
                contentContainerStyle={styles.listContainer}
                showsVerticalScrollIndicator={false}
                ListEmptyComponent={
                    <View style={styles.emptyContainer}>
                        <Ionicons
                            name="home-outline"
                            size={64}
                            color={theme.TEXT_SECONDARY}
                        />
                        <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
                            No sites found
                        </Text>
                    </View>
                }
            />
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        paddingTop: 50,
        paddingBottom: 20,
        paddingHorizontal: 20,
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginTop: 10,
    },
    headerTitle: {
        fontSize: 24,
        fontWeight: 'bold',
    },
    searchIcon: {
        padding: 8,
    },
    searchContainer: {
        padding: 16,
    },
    searchInput: {
        borderWidth: 1,
        borderRadius: 12,
        paddingHorizontal: 16,
        paddingVertical: 12,
        fontSize: 16,
    },
    filterContainer: {
        flexDirection: 'row',
        paddingHorizontal: 16,
        paddingVertical: 8,
        gap: 8,
    },
    filterButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        borderWidth: 1,
    },
    filterButtonText: {
        fontSize: 14,
        fontWeight: '600',
    },
    listContainer: {
        padding: 16,
    },
    siteCard: {
        borderRadius: 16,
        marginBottom: 16,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
        overflow: 'hidden',
    },
    siteHeader: {
        position: 'relative',
    },
    statusBadge: {
        position: 'absolute',
        top: 12,
        right: 12,
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        zIndex: 1,
    },
    statusText: {
        fontSize: 12,
        fontWeight: 'bold',
    },
    siteImage: {
        width: '100%',
        height: 200,
        justifyContent: 'center',
        alignItems: 'center',
    },
    siteInfo: {
        padding: 16,
    },
    siteName: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    siteLocation: {
        fontSize: 14,
        marginBottom: 8,
    },
    siteDetails: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 4,
    },
    siteArea: {
        fontSize: 14,
    },
    sitePrice: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    siteOwner: {
        fontSize: 12,
        fontStyle: 'italic',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 24,
    },
    errorText: {
        fontSize: 16,
        textAlign: 'center',
        marginBottom: 16,
    },
    retryButton: {
        paddingHorizontal: 24,
        paddingVertical: 12,
        borderRadius: 8,
    },
    retryButtonText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    emptyContainer: {
        alignItems: 'center',
        paddingVertical: 64,
    },
    emptyText: {
        fontSize: 16,
        marginTop: 16,
    },
});

export default SiteList;
