import React, {
    useState,
    useRef,
    useEffect,
    useContext,
    useCallback,
} from 'react';
import {
    View,
    Image,
    ActivityIndicator,
    ScrollView,
    Text,
    Platform,
    KeyboardAvoidingView,
    StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useQuery, useMutation } from '@tanstack/react-query';
import queryClient from '../../api/queryClient';
import { useFormik } from 'formik';
import { ThemeContext } from '../../context/ThemeContext';
import { fetchUserProfile } from '../../api/user/userApi';
import { showToast } from '../../utils/showToast';
import BackButton from '../Components/Shared/BackButton';
import DocumentPreviewModal from '../Components/Profile/DocumentPreviewModal';
import UserDetailsStep from './UserDetailsStep';
import AadhaarStep from './AadhaarStep';
import PanStep from './PanStep';
import ContractorDetailsStep from './ContractorDetailsStep';
import { styles } from './styles';
import {
    updateContractorProfile,
    createContractorApplication,
} from '../../api/contractor/contractorApi';
import { validationSchema } from '../../utils/validateSchema';

const ContractorForm = () => {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const { editData } = useLocalSearchParams();
    const [step, setStep] = useState('userDetails');
    const [showDocumentPreviewModal, setShowDocumentPreviewModal] =
        useState(false);
    const [selectedDocument, setSelectedDocument] = useState(null);
    const scrollViewRef = useRef(null);
    const [isSubmitting, setIsSubmitting] = useState(false);

    let parsedEditData = null;
    try {
        parsedEditData = editData ? JSON.parse(editData) : null;
    } catch (error) {
        showToast(
            'error',
            'Invalid Data',
            'Failed to load existing application data.'
        );
    }
    const isEditing = !!parsedEditData;
    const contractorId = parsedEditData?.id;

    useEffect(() => {
        if (scrollViewRef.current) {
            scrollViewRef.current.scrollTo({ y: 0, animated: true });
        }
    }, [step]);

    const {
        data: user,
        isLoading,
        isError,
        error,
    } = useQuery({
        queryKey: ['userProfile'],
        queryFn: fetchUserProfile,
        refetchInterval: isEditing ? 30000 : false,
        refetchIntervalInBackground: false,
        enabled: true,
        onError: () => {
            showToast('error', 'Error', 'Failed to fetch user profile.');
        },
    });

    const { mutateAsync: updateContractor, isLoading: isUpdating } =
        useMutation({
            mutationFn: ({ contractorId, data }) => {
                return updateContractorProfile(contractorId, data);
            },
            onSuccess: () => {
                setIsSubmitting(false);
                queryClient.invalidateQueries({
                    queryKey: ['contractorApplication'],
                });
                showToast(
                    'success',
                    'Success',
                    'Contractor application updated successfully.'
                );
                router.replace('/Profile/Applications');
            },
            onError: (error) => {
                setIsSubmitting(false);
                if (error.response?.status === 409) {
                    showToast(
                        'error',
                        'Error',
                        'You are already registered as a Contractor.'
                    );
                } else if (error.response?.status === 400) {
                    showToast(
                        'error',
                        'Invalid Data',
                        error.response.data.error ||
                            'Validation failed. Please check your inputs.'
                    );
                } else if (error.response?.status === 404) {
                    showToast(
                        'error',
                        'Not Found',
                        error.response.data.error ||
                            'Contractor application not found.'
                    );
                } else {
                    showToast(
                        'error',
                        'Submission Error',
                        'Failed to submit application.'
                    );
                }
            },
        });

    const { mutateAsync: createContractor, isLoading: isCreating } =
        useMutation({
            mutationFn: createContractorApplication,
            onSuccess: () => {
                setIsSubmitting(false);
                queryClient.invalidateQueries({
                    queryKey: ['contractorApplication'],
                });
                router.replace('/Contractors/ContractorSuccess');
            },
            onError: (error) => {
                setIsSubmitting(false);
                if (error.response?.status === 400) {
                    showToast(
                        'error',
                        'Invalid Data',
                        error.response.data.error ||
                            'Validation failed. Please check your inputs.'
                    );
                } else if (error.response?.status === 409) {
                    showToast(
                        'error',
                        'Error',
                        'You are already registered as a Contractor.'
                    );
                } else if (error.response?.status === 403) {
                    showToast(
                        'error',
                        'Error',
                        'You are already submitted partnership request as site scout.'
                    );
                } else {
                    showToast(
                        'error',
                        'Submission Error',
                        'Failed to submit application.'
                    );
                }
            },
        });

    const formik = useFormik({
        initialValues: {
            aadhaarNumber: parsedEditData?.aadhaarNumber || '',
            nameOnAadhaar: parsedEditData?.nameOnAadhaar || '',
            dateOfBirth: parsedEditData?.dateOfBirth
                ? new Date(parsedEditData.dateOfBirth)
                : '',
            gender: parsedEditData?.gender || '',
            address: parsedEditData?.address || '',
            panNumber: parsedEditData?.panNumber || '',
            panName: parsedEditData?.panName || '',
            panDateOfBirth: parsedEditData?.panDateOfBirth
                ? new Date(parsedEditData.panDateOfBirth)
                : '',
            specialties: Array.isArray(parsedEditData?.specialties)
                ? parsedEditData.specialties.filter(
                      (item) => item.trim() !== ''
                  )
                : [],
            experience: parsedEditData?.experience?.toString() || '',
            serviceAreas: Array.isArray(parsedEditData?.serviceAreas)
                ? parsedEditData.serviceAreas
                : [],
            aadhaarDocument: parsedEditData?.aadhaarDocument || null,
            panDocument: parsedEditData?.panDocument || null,
            contractorId: contractorId || null,
        },
        validationSchema,
        validateOnChange: true,
        validateOnBlur: true,
        onSubmit: async (values) => {
            setIsSubmitting(true);
            const formPayload = new FormData();

            // Append fields matching backend validators
            formPayload.append('aadhaarNumber', values.aadhaarNumber);
            formPayload.append('nameOnAadhaar', values.nameOnAadhaar);
            formPayload.append('gender', values.gender);
            formPayload.append('address', values.address);
            formPayload.append('panNumber', values.panNumber);
            formPayload.append('panName', values.panName);
            formPayload.append('experience', values.experience);

            if (
                values.dateOfBirth instanceof Date &&
                !isNaN(values.dateOfBirth)
            ) {
                formPayload.append(
                    'dateOfBirth',
                    values.dateOfBirth.toISOString()
                );
            }
            if (
                values.panDateOfBirth instanceof Date &&
                !isNaN(values.panDateOfBirth)
            ) {
                formPayload.append(
                    'panDateOfBirth',
                    values.panDateOfBirth.toISOString()
                );
            }

            // Append serviceAreas as an array (max 10)
            const serviceAreas = Array.isArray(values.serviceAreas)
                ? values.serviceAreas
                      .slice(0, 10)
                      .filter((item) => item.trim() !== '')
                : [];
            serviceAreas.forEach((area) => {
                formPayload.append('serviceAreas[]', area);
            });

            // Append specialties as an array (max 15, optional), only if non-empty
            const specialties = Array.isArray(values.specialties)
                ? values.specialties
                      .slice(0, 15)
                      .filter((item) => item.trim() !== '')
                : [];
            specialties.forEach((specialty) => {
                formPayload.append('specialties[]', specialty);
            });

            // Append documents if they are new (not strings)
            if (
                values.aadhaarDocument &&
                typeof values.aadhaarDocument !== 'string' &&
                values.aadhaarDocument.uri
            ) {
                formPayload.append('aadhaarDocument', {
                    uri: values.aadhaarDocument.uri,
                    name: values.aadhaarDocument.name || 'aadhaarDocument.jpg',
                    type: values.aadhaarDocument.type || 'image/jpeg',
                });
            }

            if (
                values.panDocument &&
                typeof values.panDocument !== 'string' &&
                values.panDocument.uri
            ) {
                formPayload.append('panDocument', {
                    uri: values.panDocument.uri,
                    name: values.panDocument.name || 'panDocument.jpg',
                    type: values.panDocument.type || 'image/jpeg',
                });
            }
            formik.setSubmitting(true);
            // Trigger appropriate mutation
            if (isEditing) {
                await updateContractor({ contractorId, data: formPayload });
            } else {
                await createContractor(formPayload);
            }
            formik.setSubmitting(false);
        },
    });

    const handlePreviewDocument = useCallback((document) => {
        if (document) {
            const uri = typeof document === 'string' ? document : document.uri;
            setSelectedDocument(uri);
            setShowDocumentPreviewModal(true);
        }
    }, []);

    if (isLoading) {
        return <ActivityIndicator size="large" color={theme.PRIMARY} />;
    }

    if (isError) {
        return (
            <Text style={{ color: theme.TEXT_PRIMARY }}>
                Error: {error.message}
            </Text>
        );
    }

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            />
            <ScrollView
                ref={scrollViewRef}
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
            >
                <BackButton color={theme.WHITE} />
                <View style={styles.backgroundContainer}>
                    <Image
                        source={require('../../assets/images/background.png')}
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                    <LinearGradient
                        colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                        style={styles.backgroundOverlay}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 1 }}
                    />
                </View>
                <View style={styles.contentContainer}>
                    <View
                        style={[
                            styles.formContainer,
                            {
                                shadowColor: theme.SHADOW,
                                backgroundColor: theme.CARD,
                            },
                        ]}
                    >
                        <Text style={[styles.title, { color: theme.PRIMARY }]}>
                            {isEditing
                                ? 'Edit Contractor Application'
                                : 'Contractor Application'}
                        </Text>
                        <Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            {isEditing
                                ? 'Update your contractor details'
                                : 'Apply to become a trusted contractor'}
                        </Text>
                        <Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Step{' '}
                            {[
                                'userDetails',
                                'aadhaar',
                                'pan',
                                'contractorDetails',
                            ].indexOf(step) + 1}{' '}
                            of 4
                        </Text>
                        {step === 'userDetails' && (
                            <UserDetailsStep
                                theme={theme}
                                user={user}
                                setStep={setStep}
                            />
                        )}
                        {step === 'aadhaar' && (
                            <AadhaarStep
                                formik={formik}
                                theme={theme}
                                setStep={setStep}
                                handlePreviewDocument={handlePreviewDocument}
                                isUploading={isCreating || isUpdating}
                            />
                        )}
                        {step === 'pan' && (
                            <PanStep
                                formik={formik}
                                theme={theme}
                                setStep={setStep}
                                handlePreviewDocument={handlePreviewDocument}
                                isUploading={isCreating || isUpdating}
                            />
                        )}
                        {step === 'contractorDetails' && (
                            <ContractorDetailsStep
                                formik={formik}
                                theme={theme}
                                setStep={setStep}
                                isSubmitting={isSubmitting}
                            />
                        )}
                    </View>
                </View>
            </ScrollView>
            <DocumentPreviewModal
                visible={showDocumentPreviewModal}
                documentUrl={selectedDocument}
                documentType=""
                onClose={() => {
                    setShowDocumentPreviewModal(false);
                    setSelectedDocument(null);
                }}
            />
        </KeyboardAvoidingView>
    );
};

export default ContractorForm;
