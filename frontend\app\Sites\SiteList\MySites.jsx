import React, { useState, useEffect, useContext } from 'react';
import {
    View,
    Text,
    FlatList,
    TouchableOpacity,
    ActivityIndicator,
    Alert,
    Image,
    StyleSheet,
} from 'react-native';
import { useRouter } from 'expo-router';
import { privateAPIClient } from '../../../api';
import { ThemeContext } from '../../../context/ThemeContext';

const MySites = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const [sites, setSites] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const fetchSites = async () => {
        setLoading(true);
        setError(null);
        try {
            const response = await privateAPIClient.get(
                '/site-service/api/v1/user/sites'
            );
            setSites(response.data.sites || []);
        } catch (err) {
            setError('Failed to fetch sites. Please try again.');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchSites();
    }, []);

    const handleRetry = () => {
        fetchSites();
    };

    const formatAddress = (item) => {
        return (
            `${item.addressLine1 || ''}, ${item.addressLine2 || ''}, ${item.district || ''}, ${item.state || ''} ${item.pincode || ''}`.trim() ||
            'Address not available'
        );
    };

    const getStatusStyle = (status) => {
        switch (status) {
            case 'approved':
                return { color: '#28a745', backgroundColor: '#d4edda' }; // Green for approved
            case 'rejected':
                return { color: '#dc3545', backgroundColor: '#f8d7da' }; // Red for rejected
            default:
                return { color: '#ffc107', backgroundColor: '#fff3cd' }; // Yellow for pending/other
        }
    };

    const renderItem = ({ item }) => {
        const statusStyle = getStatusStyle(item.status);
        return (
            <TouchableOpacity
                activeOpacity={0.8}
                style={[styles.siteItem, { backgroundColor: theme.CARD }]}
                onPress={() => {
                    if (item._id) {
                        router.push({
                            pathname: 'Sites/SiteList/SiteDetailsScreen',
                            params: { siteId: item._id },
                        });
                    } else {
                        Alert.alert('Error', 'Site ID not available');
                    }
                }}
            >
                <View style={styles.cardContent}>
                    {item.thumbnail?.imageURL ? (
                        <Image
                            source={{ uri: item.thumbnail.imageURL }}
                            style={styles.thumbnail}
                            resizeMode="cover"
                            onError={() =>
                                Alert.alert('Error', 'Failed to load image')
                            }
                        />
                    ) : (
                        <View
                            style={[styles.thumbnail, styles.placeholderImage]}
                        >
                            <Text style={styles.placeholderText}>No Image</Text>
                        </View>
                    )}
                    <View style={styles.detailsContainer}>
                        <Text
                            style={[
                                styles.siteName,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                            numberOfLines={1}
                            ellipsizeMode="tail"
                        >
                            {item.name || 'Unnamed Site'}
                        </Text>
                        <Text
                            style={[
                                styles.siteDetails,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                            numberOfLines={2}
                            ellipsizeMode="tail"
                        >
                            {formatAddress(item)}
                        </Text>
                        <View style={styles.infoContainer}>
                            <Text
                                style={[
                                    styles.siteDetails,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Area: {item.plotArea || 'N/A'} sq.ft
                            </Text>
                            <Text
                                style={[
                                    styles.sitePrice,
                                    { color: theme.PRIMARY },
                                ]}
                            >
                                ₹{item.price || 'N/A'}
                            </Text>
                        </View>
                    </View>
                </View>
                <View
                    style={[
                        styles.statusContainer,
                        { backgroundColor: statusStyle.backgroundColor },
                    ]}
                >
                    <Text
                        style={[
                            styles.statusText,
                            { color: statusStyle.color },
                        ]}
                    >
                        Status:{' '}
                        {item.status
                            ? item.status.charAt(0).toUpperCase() +
                              item.status.slice(1)
                            : 'Pending'}
                    </Text>
                    {item.reasonForRejection && item.status === 'rejected' && (
                        <Text style={styles.rejectionReason}>
                            Reason: {item.reasonForRejection}
                        </Text>
                    )}
                </View>
            </TouchableOpacity>
        );
    };

    if (loading) {
        return (
            <View
                style={[styles.centered, { backgroundColor: theme.BACKGROUND }]}
            >
                <ActivityIndicator size="large" color={theme.PRIMARY} />
                <Text style={styles.loadingText}>Loading...</Text>
            </View>
        );
    }

    if (error) {
        return (
            <View
                style={[styles.centered, { backgroundColor: theme.BACKGROUND }]}
            >
                <Text style={styles.errorText}>{error}</Text>
                <TouchableOpacity
                    style={[
                        styles.retryButton,
                        { backgroundColor: theme.PRIMARY },
                    ]}
                    onPress={handleRetry}
                >
                    <Text style={styles.retryButtonText}>Retry</Text>
                </TouchableOpacity>
            </View>
        );
    }

    return (
        <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <Text style={[styles.title, { color: theme.TEXT_PRIMARY }]}>
                My Sites
            </Text>
            <FlatList
                data={sites}
                renderItem={renderItem}
                keyExtractor={(item) => item._id?.toString() || item.name}
                contentContainerStyle={styles.listContent}
                ListEmptyComponent={
                    <Text style={styles.emptyText}>No sites available</Text>
                }
                showsVerticalScrollIndicator={false}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 8,
    },
    centered: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 16,
        textAlign: 'center',
    },
    listContent: {
        paddingBottom: 8,
    },
    siteItem: {
        borderRadius: 16, // Modern rounded corners
        marginBottom: 8,
        overflow: 'hidden',
        elevation: 4, // Subtle shadow for depth
        shadowColor: '#000',
        shadowOpacity: 0.1,
        shadowRadius: 4,
        shadowOffset: { width: 0, height: 2 },
    },
    cardContent: {
        flexDirection: 'row',
        padding: 16, // Ample padding for clarity
    },
    thumbnail: {
        width: 100,
        height: 100,
        borderRadius: 12, // Rounded image for modern feel
        backgroundColor: '#f0f0f0',
    },
    placeholderImage: {
        width: 100,
        height: 100,
        borderRadius: 12,
        backgroundColor: '#eee',
        justifyContent: 'center',
        alignItems: 'center',
    },
    placeholderText: {
        color: '#999',
        fontSize: 14,
    },
    detailsContainer: {
        flex: 1,
        marginLeft: 16, // Space between image and text
    },
    siteName: {
        fontSize: 18,
        fontWeight: '600', // Semi-bold for modern typography
        marginBottom: 4,
    },
    siteDetails: {
        fontSize: 14,
        marginBottom: 4,
    },
    infoContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: 4,
    },
    sitePrice: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    statusContainer: {
        padding: 12,
        borderTopWidth: 1,
        borderTopColor: '#eee', // Subtle separator
    },
    statusText: {
        fontSize: 14,
        fontWeight: '600',
    },
    rejectionReason: {
        fontSize: 12,
        color: '#666',
        marginTop: 4,
    },
    emptyText: {
        textAlign: 'center',
        fontSize: 16,
        color: '#999',
        marginTop: 20,
    },
    loadingText: {
        marginTop: 8,
        fontSize: 16,
    },
    errorText: {
        fontSize: 16,
        color: 'red',
        textAlign: 'center',
        marginBottom: 16,
    },
    retryButton: {
        padding: 10,
        borderRadius: 8,
    },
    retryButtonText: {
        color: '#fff',
        fontWeight: 'bold',
    },
});

export default MySites;
