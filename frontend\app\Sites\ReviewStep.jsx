import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { styles } from './styles';

const ReviewStep = ({ theme, formik, setStep, isSubmitting, onSubmit }) => {
    const { values } = formik;

    const renderReviewSection = (title, icon, data) => (
        <View style={[styles.reviewSection, { backgroundColor: theme.ACCENT }]}>
            <View style={styles.reviewSectionHeader}>
                <Ionicons name={icon} size={24} color={theme.PRIMARY} />
                <Text style={[styles.reviewSectionTitle, { color: theme.PRIMARY }]}>
                    {title}
                </Text>
            </View>
            {data.map((item, index) => (
                <View key={index} style={styles.reviewItem}>
                    <Text style={[styles.reviewLabel, { color: theme.TEXT_SECONDARY }]}>
                        {item.label}:
                    </Text>
                    <Text style={[styles.reviewValue, { color: theme.TEXT_PRIMARY }]}>
                        {item.value || 'Not provided'}
                    </Text>
                </View>
            ))}
        </View>
    );

    const siteDetailsData = [
        { label: 'Site Name', value: values.name },
        { label: 'Address Line 1', value: values.addressLine1 },
        { label: 'Address Line 2', value: values.addressLine2 },
        { label: 'Landmark', value: values.landmark },
        { label: 'Pincode', value: values.pincode },
        { label: 'State', value: values.state },
        { label: 'District', value: values.district },
        { label: 'Plot Area', value: values.plotArea ? `${values.plotArea} sqft` : '' },
        { label: 'Price', value: values.price ? `₹${values.price}` : '' },
        { label: 'Site Images', value: values.siteImages?.length ? `${values.siteImages.length} image(s)` : 'No images' },
    ];

    const locationData = [
        { label: 'Location', value: values.location },
        { label: 'Village', value: values.village },
        { label: 'Survey Number', value: values.surveyNumber },
        { label: 'Latitude', value: values.latitude },
        { label: 'Longitude', value: values.longitude },
    ];

    const encumbranceData = [
        { label: 'Owner Name', value: values.encOwnerName },
        { label: 'Document Number', value: values.encumbranceDocNumber },
        { label: 'Date', value: values.encumbranceDate },
        { label: 'Certificate', value: values.encumbranceCert ? 'Uploaded' : 'Not uploaded' },
    ];

    const propertyTaxData = [
        { label: 'Tax Number', value: values.propertyTaxNumber },
        { label: 'Date', value: values.propertyTaxDate },
        { label: 'Receipt', value: values.propertyTaxRec ? 'Uploaded' : 'Not uploaded' },
    ];

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Review & Submit
            </Text>
            <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
                Please review all the information before submitting your site application
            </Text>

            <ScrollView 
                style={styles.reviewContainer}
                showsVerticalScrollIndicator={false}
                nestedScrollEnabled={true}
            >
                {renderReviewSection('Site Details', 'home-outline', siteDetailsData)}
                {renderReviewSection('Location Details', 'location-outline', locationData)}
                {renderReviewSection('Encumbrance Certificate', 'document-text-outline', encumbranceData)}
                {renderReviewSection('Property Tax Receipt', 'receipt-outline', propertyTaxData)}
            </ScrollView>

            {/* Navigation Buttons */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[styles.backButton, { borderColor: theme.INPUT_BORDER }]}
                    onPress={() => setStep('propertyTax')}
                    disabled={isSubmitting}
                    accessibilityLabel="Go back to property tax details"
                    accessibilityRole="button"
                >
                    <Text style={[styles.backButtonText, { color: theme.PRIMARY }]}>
                        Back
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.submitButton}
                    onPress={onSubmit}
                    disabled={isSubmitting}
                    accessibilityLabel="Submit site application"
                    accessibilityRole="button"
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.submitButtonGradient}
                    >
                        <Text
                            style={[
                                styles.submitButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            {isSubmitting ? 'Submitting...' : 'Submit Application'}
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default ReviewStep;
