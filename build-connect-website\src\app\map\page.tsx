'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

export default function MapPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-foreground">Map Explorer</h1>
        <p className="mt-2 text-muted-foreground">
          Explore properties on an interactive map
        </p>
      </div>

      {/* Search and Filters */}
      <div className="mb-8 flex flex-col gap-4 sm:flex-row">
        <div className="flex-1">
          <Input placeholder="Search by location..." />
        </div>
        <Button variant="outline">Filters</Button>
        <Button>Search</Button>
      </div>

      {/* Map Container */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Map */}
        <div className="lg:col-span-2">
          <Card className="h-[600px]">
            <CardContent className="flex h-full items-center justify-center p-6">
              <div className="text-center">
                <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-muted flex items-center justify-center">
                  <span className="text-2xl">🗺️</span>
                </div>
                <h3 className="text-lg font-semibold">Interactive Map</h3>
                <p className="text-muted-foreground">
                  Map integration will be implemented here
                </p>
                <p className="mt-2 text-sm text-muted-foreground">
                  Google Maps or Mapbox integration coming soon
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Property List */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Properties in View</h3>
          {[1, 2, 3, 4, 5].map((i) => (
            <Card key={i} className="cursor-pointer transition-all hover:shadow-card-hover">
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Villa in Whitefield</CardTitle>
                <CardDescription className="text-sm">
                  3 BHK • 2000 sq ft
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                  <span className="font-bold text-primary">₹85 Lakhs</span>
                  <Button size="sm" variant="outline">
                    View
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Map Controls */}
      <div className="mt-8 flex flex-wrap gap-4">
        <Button variant="outline" size="sm">
          🏠 Residential
        </Button>
        <Button variant="outline" size="sm">
          🏢 Commercial
        </Button>
        <Button variant="outline" size="sm">
          🌾 Agricultural
        </Button>
        <Button variant="outline" size="sm">
          🏭 Industrial
        </Button>
      </div>
    </div>
  )
}
