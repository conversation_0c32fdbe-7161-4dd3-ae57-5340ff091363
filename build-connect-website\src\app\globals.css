@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import "tailwindcss";

:root {
  /* Light theme colors based on mobile app */
  --background: #F5F7FA;
  --foreground: #212121;
  --card: #FFFFFF;
  --card-foreground: #212121;
  --primary: #2A8E9E;
  --primary-foreground: #FFFFFF;
  --secondary: #001d3d;
  --secondary-foreground: #FFFFFF;
  --accent: #E6F3F7;
  --accent-foreground: #212121;
  --muted: #f9f9f9;
  --muted-foreground: #757575;
  --destructive: #D32F2F;
  --destructive-foreground: #FFFFFF;
  --border: #e0e0e0;
  --input: #f9f9f9;
  --ring: #2A8E9E;
  --success: #4CAF50;
  --warning: #FFB300;
  --info: #87CEFA;
  --text-secondary: #757575;
  --gray-light: #E0E0E0;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-info: var(--info);
  --color-text-secondary: var(--text-secondary);
  --color-gray-light: var(--gray-light);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.dark {
  /* Dark theme colors */
  --background: #121212;
  --foreground: #E0E0E0;
  --card: #1E1E1E;
  --card-foreground: #E0E0E0;
  --accent: #1B2A33;
  --accent-foreground: #E0E0E0;
  --muted: #1E1E1E;
  --muted-foreground: #B0B0B0;
  --border: #333333;
  --input: #1E1E1E;
  --text-secondary: #B0B0B0;
  --gray-light: #B0B0B0;
}

* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', system-ui, sans-serif;
  font-feature-settings: 'cv11', 'ss01';
  font-variation-settings: 'opsz' 32;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px var(--ring);
}

/* Component styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: none;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--ring);
}

.btn:disabled {
  pointer-events: none;
  opacity: 0.5;
}

.btn-primary {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

.btn-primary:hover {
  background-color: color-mix(in srgb, var(--primary) 90%, black);
}

.btn-secondary {
  background-color: var(--secondary);
  color: var(--secondary-foreground);
}

.btn-secondary:hover {
  background-color: color-mix(in srgb, var(--secondary) 90%, black);
}

.btn-outline {
  border: 1px solid var(--border);
  background-color: transparent;
  color: var(--foreground);
}

.btn-outline:hover {
  background-color: var(--accent);
  color: var(--accent-foreground);
}

.btn-ghost {
  background-color: transparent;
  color: var(--foreground);
}

.btn-ghost:hover {
  background-color: var(--accent);
  color: var(--accent-foreground);
}

.btn-sm {
  height: 32px;
  padding: 0 12px;
  font-size: 12px;
}

.btn-md {
  height: 40px;
  padding: 8px 16px;
}

.btn-lg {
  height: 48px;
  padding: 12px 32px;
  font-size: 16px;
}

.card {
  border-radius: 12px;
  border: 1px solid var(--border);
  background-color: var(--card);
  color: var(--card-foreground);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.input {
  display: flex;
  height: 40px;
  width: 100%;
  border-radius: 8px;
  border: 1px solid var(--border);
  background-color: var(--input);
  padding: 8px 12px;
  font-size: 14px;
  transition: all 0.2s ease-in-out;
}

.input:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--ring);
}

.input:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.input::placeholder {
  color: var(--muted-foreground);
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    transform: translateY(10px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}

@keyframes slideDown {
  from { 
    transform: translateY(-10px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}
