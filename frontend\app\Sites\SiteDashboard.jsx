import React, { useState, useContext } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    SafeAreaView,
    Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';
import BackButton from '../Components/Shared/BackButton';

const { width } = Dimensions.get('window');

const SiteDashboard = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const [selectedPeriod, setSelectedPeriod] = useState('month');

    // Mock site owner data
    const siteStats = {
        totalSites: 8,
        activeSites: 5,
        soldSites: 3,
        totalValue: 12500000,
        monthlyInquiries: 23,
        averagePrice: 1562500,
        rating: 4.6,
        reviews: 42,
        viewsThisMonth: 1240,
        interestedBuyers: 15,
    };

    const recentActivities = [
        {
            id: 1,
            type: 'inquiry',
            title: 'New inquiry received',
            description: 'Buyer interested in Whitefield plot',
            time: '1 hour ago',
            icon: 'chatbubble',
            color: '#2196F3',
        },
        {
            id: 2,
            type: 'listing',
            title: 'Site listing updated',
            description: 'Price updated for Electronic City plot',
            time: '3 hours ago',
            icon: 'create',
            color: '#4CAF50',
        },
        {
            id: 3,
            type: 'sale',
            title: 'Site sold successfully',
            description: 'Plot in Sarjapur - ₹18 Lakh',
            time: '2 days ago',
            icon: 'checkmark-circle',
            color: '#FF9800',
        },
        {
            id: 4,
            type: 'view',
            title: 'High interest site',
            description: 'Hebbal plot viewed 45 times this week',
            time: '3 days ago',
            icon: 'eye',
            color: '#9C27B0',
        },
    ];

    const quickActions = [
        {
            id: 'add-site',
            title: 'Add New Site',
            icon: 'add-circle',
            color: '#4CAF50',
            onPress: () => router.push('/Sites/SiteForm'),
        },
        {
            id: 'manage-sites',
            title: 'Manage Sites',
            icon: 'list',
            color: '#2196F3',
            onPress: () => router.push('/Sites/SiteList'),
        },
        {
            id: 'inquiries',
            title: 'View Inquiries',
            icon: 'mail',
            color: '#FF9800',
            onPress: () => router.push('/Sites/Inquiries'),
        },
        {
            id: 'analytics',
            title: 'Site Analytics',
            icon: 'analytics',
            color: '#9C27B0',
            onPress: () => router.push('/Sites/Analytics'),
        },
    ];

    const StatCard = ({ title, value, subtitle, icon, color }) => (
        <View style={[styles.statCard, { backgroundColor: theme.CARD }]}>
            <View style={styles.statHeader}>
                <View style={[styles.statIcon, { backgroundColor: color + '20' }]}>
                    <Ionicons name={icon} size={24} color={color} />
                </View>
                <Text style={[styles.statValue, { color: theme.TEXT_PRIMARY }]}>
                    {value}
                </Text>
            </View>
            <Text style={[styles.statTitle, { color: theme.TEXT_SECONDARY }]}>
                {title}
            </Text>
            {subtitle && (
                <Text style={[styles.statSubtitle, { color: theme.TEXT_SECONDARY }]}>
                    {subtitle}
                </Text>
            )}
        </View>
    );

    const ActivityItem = ({ activity }) => (
        <View style={[styles.activityItem, { backgroundColor: theme.CARD }]}>
            <View style={[styles.activityIcon, { backgroundColor: activity.color + '20' }]}>
                <Ionicons name={activity.icon} size={20} color={activity.color} />
            </View>
            <View style={styles.activityContent}>
                <Text style={[styles.activityTitle, { color: theme.TEXT_PRIMARY }]}>
                    {activity.title}
                </Text>
                <Text style={[styles.activityDescription, { color: theme.TEXT_SECONDARY }]}>
                    {activity.description}
                </Text>
                <Text style={[styles.activityTime, { color: theme.TEXT_SECONDARY }]}>
                    {activity.time}
                </Text>
            </View>
        </View>
    );

    const QuickActionButton = ({ action }) => (
        <TouchableOpacity
            style={[styles.quickActionButton, { backgroundColor: theme.CARD }]}
            onPress={action.onPress}
        >
            <View style={[styles.quickActionIcon, { backgroundColor: action.color + '20' }]}>
                <Ionicons name={action.icon} size={28} color={action.color} />
            </View>
            <Text style={[styles.quickActionText, { color: theme.TEXT_PRIMARY }]}>
                {action.title}
            </Text>
        </TouchableOpacity>
    );

    return (
        <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <LinearGradient
                colors={[theme.GRADIENT_PRIMARY, theme.GRADIENT_SECONDARY]}
                style={styles.header}
            >
                <BackButton color={theme.WHITE} />
                <View style={styles.headerContent}>
                    <Text style={[styles.headerTitle, { color: theme.WHITE }]}>
                        Site Dashboard
                    </Text>
                    <Text style={[styles.headerSubtitle, { color: theme.WHITE + 'CC' }]}>
                        Manage your property listings
                    </Text>
                </View>
            </LinearGradient>

            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                {/* Stats Overview */}
                <View style={styles.section}>
                    <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                        Overview
                    </Text>
                    <View style={styles.statsGrid}>
                        <StatCard
                            title="Total Sites"
                            value={siteStats.totalSites}
                            icon="home"
                            color="#4CAF50"
                        />
                        <StatCard
                            title="Active Listings"
                            value={siteStats.activeSites}
                            icon="list"
                            color="#2196F3"
                        />
                        <StatCard
                            title="Sites Sold"
                            value={siteStats.soldSites}
                            icon="checkmark-circle"
                            color="#FF9800"
                        />
                        <StatCard
                            title="Monthly Inquiries"
                            value={siteStats.monthlyInquiries}
                            icon="mail"
                            color="#9C27B0"
                        />
                    </View>
                </View>

                {/* Financial Stats */}
                <View style={styles.section}>
                    <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                        Financial Overview
                    </Text>
                    <View style={styles.financialCard}>
                        <LinearGradient
                            colors={[theme.PRIMARY, theme.SECONDARY]}
                            style={styles.financialGradient}
                        >
                            <Text style={[styles.financialTitle, { color: theme.WHITE }]}>
                                Total Portfolio Value
                            </Text>
                            <Text style={[styles.financialValue, { color: theme.WHITE }]}>
                                ₹{(siteStats.totalValue / 100000).toFixed(1)}L
                            </Text>
                            <Text style={[styles.financialSubtitle, { color: theme.WHITE + 'CC' }]}>
                                Average: ₹{(siteStats.averagePrice / 100000).toFixed(1)}L per site
                            </Text>
                        </LinearGradient>
                    </View>
                </View>

                {/* Quick Actions */}
                <View style={styles.section}>
                    <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                        Quick Actions
                    </Text>
                    <View style={styles.quickActionsGrid}>
                        {quickActions.map((action) => (
                            <QuickActionButton key={action.id} action={action} />
                        ))}
                    </View>
                </View>

                {/* Recent Activities */}
                <View style={styles.section}>
                    <Text style={[styles.sectionTitle, { color: theme.TEXT_PRIMARY }]}>
                        Recent Activities
                    </Text>
                    <View style={styles.activitiesList}>
                        {recentActivities.map((activity) => (
                            <ActivityItem key={activity.id} activity={activity} />
                        ))}
                    </View>
                </View>
            </ScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        paddingTop: 50,
        paddingBottom: 30,
        paddingHorizontal: 20,
    },
    headerContent: {
        marginTop: 10,
    },
    headerTitle: {
        fontSize: 28,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    headerSubtitle: {
        fontSize: 16,
    },
    content: {
        flex: 1,
        paddingHorizontal: 20,
    },
    section: {
        marginBottom: 24,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 16,
    },
    statsGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },
    statCard: {
        width: (width - 60) / 2,
        padding: 16,
        borderRadius: 16,
        marginBottom: 12,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    statHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    statIcon: {
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
    },
    statValue: {
        fontSize: 24,
        fontWeight: 'bold',
    },
    statTitle: {
        fontSize: 14,
        fontWeight: '600',
    },
    statSubtitle: {
        fontSize: 12,
        marginTop: 2,
    },
    financialCard: {
        borderRadius: 16,
        overflow: 'hidden',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        elevation: 5,
    },
    financialGradient: {
        padding: 24,
        alignItems: 'center',
    },
    financialTitle: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 8,
    },
    financialValue: {
        fontSize: 36,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    financialSubtitle: {
        fontSize: 14,
    },
    quickActionsGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },
    quickActionButton: {
        width: (width - 60) / 2,
        padding: 20,
        borderRadius: 16,
        alignItems: 'center',
        marginBottom: 12,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    quickActionIcon: {
        width: 56,
        height: 56,
        borderRadius: 28,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 12,
    },
    quickActionText: {
        fontSize: 14,
        fontWeight: '600',
        textAlign: 'center',
    },
    activitiesList: {
        gap: 12,
    },
    activityItem: {
        flexDirection: 'row',
        padding: 16,
        borderRadius: 12,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
    },
    activityIcon: {
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    activityContent: {
        flex: 1,
    },
    activityTitle: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 2,
    },
    activityDescription: {
        fontSize: 14,
        marginBottom: 4,
    },
    activityTime: {
        fontSize: 12,
    },
});

export default SiteDashboard;
