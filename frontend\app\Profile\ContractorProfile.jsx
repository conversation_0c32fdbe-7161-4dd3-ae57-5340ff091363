import React, { useContext } from 'react';
import {
    View,
    Text,
    Image,
    TouchableOpacity,
    StyleSheet,
    ScrollView,
    SafeAreaView,
    ActivityIndicator,
    Dimensions,
    StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams } from 'expo-router';
import { useQuery } from '@tanstack/react-query';
import { ThemeContext } from '../../context/ThemeContext';
import {
    fetchContractorProfileById,
    fetchContractorProfile,
} from '../../api/contractor/contractorApi';
import { fetchUserProfile } from '../../api/user/userApi';
import BackButton from '../Components/Shared/BackButton';
import PortfolioGrid from '../Components/Profile/PortfolioGrid';
import EditablePortfolioGrid from '../Components/Profile/EditablePortfolioGrid';
import { showToast } from '../../utils/showToast';

const { width } = Dimensions.get('window');

export default function ContractorProfile() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const { contractorId } = useLocalSearchParams();

    // Get current user profile to check if viewing own profile
    const { data: currentUser, isLoading: isUserLoading } = useQuery({
        queryKey: ['userProfile'],
        queryFn: fetchUserProfile,
    });

    // Get current user's contractor profile if they are a contractor
    const {
        data: currentContractorProfile,
        isLoading: isCurrentContractorLoading,
    } = useQuery({
        queryKey: ['contractorProfile'],
        queryFn: fetchContractorProfile,
        enabled: currentUser?.role === 'contractor',
        retry: false,
    });

    // Check if user is viewing their own profile
    const isOwnProfile =
        currentUser?.role === 'contractor' &&
        currentContractorProfile?.contractorId === contractorId;

    const {
        data: contractor,
        isLoading,
        isError,
        error,
    } = useQuery({
        queryKey: ['contractorProfile', contractorId],
        queryFn: () => fetchContractorProfileById(contractorId),
        enabled: !!contractorId,
    });

    const handleHire = () => {
        showToast(
            'info',
            'Coming Soon',
            'Hire functionality will be available soon!'
        );
    };
    const handleMessage = () => {
        showToast(
            'info',
            'Coming Soon',
            'Message functionality will be available soon!'
        );
    };

    const renderStars = (rating) => {
        const stars = [];
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;

        for (let i = 0; i < fullStars; i++) {
            stars.push(
                <Ionicons key={i} name="star" size={16} color="#FFD700" />
            );
        }

        if (hasHalfStar) {
            stars.push(
                <Ionicons
                    key="half"
                    name="star-half"
                    size={16}
                    color="#FFD700"
                />
            );
        }

        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            stars.push(
                <Ionicons
                    key={`empty-${i}`}
                    name="star-outline"
                    size={16}
                    color="#FFD700"
                />
            );
        }

        return stars;
    };

    if (isLoading) {
        return (
            <SafeAreaView
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <BackButton />
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={theme.PRIMARY} />
                    <Text
                        style={[
                            styles.loadingText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Loading profile...
                    </Text>
                </View>
            </SafeAreaView>
        );
    }

    if (isError) {
        return (
            <SafeAreaView
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <BackButton />
                <View style={styles.errorContainer}>
                    <Ionicons
                        name="alert-circle"
                        size={48}
                        color={theme.ERROR}
                    />
                    <Text
                        style={[
                            styles.errorText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Failed to load contractor profile
                    </Text>
                    <Text
                        style={[
                            styles.errorSubtext,
                            { color: theme.TEXT_SECONDARY },
                        ]}
                    >
                        {error?.message || 'Please try again later'}
                    </Text>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                backgroundColor={theme.PRIMARY}
            />
            <ScrollView showsVerticalScrollIndicator={false}>
                <View style={styles.backgroundImageContainer}>
                    <Image
                        source={
                            isDarkMode
                                ? require('../../assets/images/background_dark.png')
                                : require('../../assets/images/background_light.png')
                        }
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                </View>
                <BackButton color={theme.WHITE} />
                <View
                    style={{
                        flex: 1,
                        flexDirection: 'row',
                        justifyContent: 'center',
                        alignItems: 'center',
                        paddingHorizontal: 6,
                    }}
                >
                    {/* Header Section */}
                    <View style={styles.header}>
                        {/* Avatar */}
                        <View style={styles.avatarContainer}>
                            {contractor?.image ? (
                                <Image
                                    source={{ uri: contractor.image }}
                                    style={[
                                        styles.avatar,
                                        { borderColor: theme.PRIMARY },
                                    ]}
                                    resizeMode="cover"
                                />
                            ) : (
                                <View
                                    style={[
                                        styles.avatarPlaceholder,
                                        {
                                            backgroundColor:
                                                theme.INPUT_BACKGROUND,
                                            borderColor: theme.PRIMARY,
                                        },
                                    ]}
                                >
                                    <Ionicons
                                        name="person"
                                        size={50}
                                        color={theme.TEXT_SECONDARY}
                                    />
                                </View>
                            )}

                            {/* Availability Indicator */}
                            <View
                                style={[
                                    styles.availabilityIndicator,
                                    {
                                        backgroundColor: contractor?.isAvailable
                                            ? theme.SUCCESS
                                            : theme.ERROR,
                                        borderColor: theme.WHITE,
                                    },
                                ]}
                            >
                                <View style={styles.availabilityDot} />
                            </View>
                        </View>

                        <Text
                            style={[
                                styles.availability,
                                {
                                    color: contractor?.isAvailable
                                        ? theme.SUCCESS
                                        : theme.ERROR,
                                },
                            ]}
                        >
                            {contractor?.isAvailable
                                ? 'Available'
                                : 'Not Available'}
                        </Text>
                    </View>
                    <View
                        style={{
                            flexDirection: 'column',
                            flex: 1,
                            alignItems: 'center',
                            justifyContent: 'center',
                        }}
                    >
                        <Text
                            style={[styles.name, { color: theme.TEXT_PRIMARY }]}
                        >
                            {contractor?.name}
                        </Text>
                        {/* Stats Section */}
                        <View style={[styles.statsContainer]}>
                            <View style={styles.statItem}>
                                <Text
                                    style={[
                                        styles.statNumber,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    {contractor?.experience || 0}
                                </Text>
                                <Text
                                    style={[
                                        styles.statLabel,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Years Experience
                                </Text>
                            </View>

                            <View style={styles.statItem}>
                                {/* <View style={styles.ratingContainer}> */}
                                <Text
                                    style={[
                                        styles.statNumber,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    {(contractor?.ratings || 0).toFixed(1)}
                                </Text>
                                {/* </View> */}
                                <Text
                                    style={[
                                        styles.statLabel,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Rating
                                </Text>
                            </View>

                            <View style={styles.statItem}>
                                <Text
                                    style={[
                                        styles.statNumber,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    {contractor?.portfolio?.length || 0}
                                </Text>
                                <Text
                                    style={[
                                        styles.statLabel,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Projects
                                </Text>
                            </View>
                        </View>
                    </View>
                </View>

                {/* Hire Button - Only show for other profiles */}
                {!isOwnProfile && (
                    <View
                        style={{
                            flexDirection: 'row',
                            gap: 10,
                            marginHorizontal: 20,
                        }}
                    >
                        <TouchableOpacity
                            style={[
                                styles.hireButton,
                                { backgroundColor: theme.PRIMARY },
                            ]}
                            onPress={handleHire}
                            activeOpacity={0.8}
                        >
                            <LinearGradient
                                colors={[theme.PRIMARY, theme.SECONDARY]}
                                style={styles.hireButtonGradient}
                            >
                                <Ionicons
                                    name="briefcase"
                                    size={20}
                                    color={theme.WHITE}
                                />
                                <Text
                                    style={[
                                        styles.hireButtonText,
                                        { color: theme.WHITE },
                                    ]}
                                >
                                    Hire
                                </Text>
                            </LinearGradient>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[
                                styles.hireButton,
                                { backgroundColor: theme.WHITE },
                            ]}
                            onPress={handleMessage}
                            activeOpacity={0.8}
                        >
                            <LinearGradient
                                colors={[theme.WHITE, theme.GRAY_LIGHT]}
                                style={styles.hireButtonGradient}
                            >
                                <Ionicons
                                    name="chatbubbles-outline"
                                    size={20}
                                    color={theme.PRIMARY}
                                />
                                <Text
                                    style={[
                                        styles.hireButtonText,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    Message
                                </Text>
                            </LinearGradient>
                        </TouchableOpacity>
                    </View>
                )}

                {/* Service Areas Section */}
                <View style={[styles.section, { backgroundColor: theme.CARD }]}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Service Areas
                    </Text>
                    <View style={styles.serviceAreasContainer}>
                        {contractor?.serviceAreas &&
                        contractor.serviceAreas.length > 0 ? (
                            contractor.serviceAreas.map((area, index) => (
                                <View
                                    key={index}
                                    style={[
                                        styles.serviceAreaTag,
                                        {
                                            backgroundColor:
                                                theme.PRIMARY + '20',
                                            borderColor: theme.PRIMARY,
                                        },
                                    ]}
                                >
                                    <Text
                                        style={[
                                            styles.serviceAreaText,
                                            { color: theme.PRIMARY },
                                        ]}
                                    >
                                        {area}
                                    </Text>
                                </View>
                            ))
                        ) : (
                            <Text
                                style={[
                                    styles.noDataText,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                No service areas specified
                            </Text>
                        )}
                    </View>
                </View>

                {/* Specialties Section */}
                <View style={[styles.section, { backgroundColor: theme.CARD }]}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Specialties
                    </Text>
                    <View style={styles.serviceAreasContainer}>
                        {contractor?.specialties &&
                        contractor.specialties.length > 0 ? (
                            contractor.specialties.map((specialty, index) => (
                                <View
                                    key={index}
                                    style={[
                                        styles.specialtyTag,
                                        {
                                            backgroundColor:
                                                theme.PRIMARY + '20',
                                            borderColor: theme.PRIMARY,
                                        },
                                    ]}
                                >
                                    <Text
                                        style={[
                                            styles.specialtyText,
                                            { color: theme.PRIMARY },
                                        ]}
                                    >
                                        {specialty}
                                    </Text>
                                </View>
                            ))
                        ) : (
                            <Text
                                style={[
                                    styles.noDataText,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                No specialties specified
                            </Text>
                        )}
                    </View>
                </View>

                {/* Portfolio Section */}
                {isOwnProfile ? (
                    <EditablePortfolioGrid
                        portfolio={contractor?.portfolio}
                        contractorId={contractorId}
                    />
                ) : (
                    <PortfolioGrid portfolio={contractor?.portfolio} />
                )}
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 32,
    },
    errorText: {
        fontSize: 18,
        fontWeight: '600',
        marginTop: 16,
        textAlign: 'center',
    },
    errorSubtext: {
        fontSize: 14,
        marginTop: 8,
        textAlign: 'center',
    },
    backgroundImageContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: width * 0.7,
        opacity: 0.9,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    header: {
        alignItems: 'center',
        paddingHorizontal: 6,
    },
    avatarContainer: {
        position: 'relative',
        marginBottom: 12,
    },
    avatar: {
        width: 90,
        height: 90,
        borderRadius: 45,
        borderWidth: 3,
    },
    avatarPlaceholder: {
        width: 90,
        height: 90,
        borderRadius: 45,
        borderWidth: 3,
        alignItems: 'center',
        justifyContent: 'center',
    },
    availabilityIndicator: {
        position: 'absolute',
        bottom: 8,
        right: 8,
        width: 12,
        height: 12,
        borderRadius: 6,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
    },
    availabilityDot: {
        width: 4,
        height: 4,
    },
    name: {
        fontSize: 20,
        fontWeight: '700',
        marginBottom: 4,
    },
    availability: {
        fontSize: 14,
        fontWeight: '500',
        marginBottom: 12,
        textAlign: 'center',
    },
    statsContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 10,
        padding: 4,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
    },
    statItem: {
        flex: 1,
        width: 'fit-content',
        alignItems: 'center',
        justifyContent: 'center',
        marginHorizontal: 4,
    },
    statNumber: {
        fontSize: 16,
        fontWeight: '700',
        marginBottom: 2,
    },
    statLabel: {
        fontSize: 11,
        textAlign: 'center',
        opacity: 0.8,
    },
    ratingText: {
        fontSize: 11,
        fontWeight: '600',
    },
    section: {
        marginHorizontal: 16,
        marginTop: 16,
        marginBottom: 2,
        borderRadius: 12,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 12,
    },
    serviceAreasContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        gap: 8,
    },
    serviceAreaTag: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        borderWidth: 1,
    },
    serviceAreaText: {
        fontSize: 14,
        fontWeight: '500',
    },
    specialtyTag: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        borderWidth: 1,
    },
    specialtyText: {
        fontSize: 14,
        fontWeight: '500',
    },
    noDataText: {
        fontSize: 14,
        fontStyle: 'italic',
    },
    hireButton: {
        borderRadius: 10,
        overflow: 'hidden',
        width: '50%',
        marginBottom: 10,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 4.65,
        elevation: 8,
    },
    hireButtonGradient: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 12,
        paddingHorizontal: 16,
    },
    hireButtonText: {
        fontSize: 16,
        fontWeight: '700',
        marginLeft: 16,
    },
});
