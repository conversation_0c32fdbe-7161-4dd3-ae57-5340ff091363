import React, {
    useState,
    useRef,
    useEffect,
    useContext,
    useCallback,
} from 'react';
import {
    View,
    Image,
    ActivityIndicator,
    ScrollView,
    Text,
    Platform,
    KeyboardAvoidingView,
    StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useQuery, useMutation } from '@tanstack/react-query';
import queryClient from '../../api/queryClient';
import { useFormik } from 'formik';
import { ThemeContext } from '../../context/ThemeContext';
import { fetchUserProfile } from '../../api/user/userApi';
import { showToast } from '../../utils/showToast';
import BackButton from '../Components/Shared/BackButton';
import DocumentPreviewModal from '../Components/Profile/DocumentPreviewModal';
import UserDetailsStep from './UserDetailsStep';
import SiteDetailsStep from './SiteDetailsStep';
import LocationStep from './LocationStep';
import EncumbranceStep from './EncumbranceStep';
import PropertyTaxStep from './PropertyTaxStep';
import ReviewStep from './ReviewStep';
import { styles } from './styles';
// Note: These API functions need to be implemented
// import {
//     createSiteApplication,
//     updateSiteProfile,
// } from '../../api/sites/siteApi';
import { completeSiteSchema } from './validations/siteValidations';

const SiteForm = () => {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const { editData } = useLocalSearchParams();
    const [step, setStep] = useState('userDetails');
    const [showDocumentPreviewModal, setShowDocumentPreviewModal] =
        useState(false);
    const [selectedDocument, setSelectedDocument] = useState(null);
    const scrollViewRef = useRef(null);
    const [isSubmitting, setIsSubmitting] = useState(false);

    let parsedEditData = null;
    let isEditing = false;

    try {
        if (editData) {
            parsedEditData = JSON.parse(editData);
            isEditing = true;
        }
    } catch (error) {
        console.error('Error parsing edit data:', error);
    }

    // Fetch user profile
    const {
        data: user,
        isLoading: isUserLoading,
        error: userError,
    } = useQuery({
        queryKey: ['userProfile'],
        queryFn: fetchUserProfile,
        staleTime: 5 * 60 * 1000,
    });

    // Form validation and state management
    const formik = useFormik({
        initialValues: {
            name: parsedEditData?.name || '',
            addressLine1: parsedEditData?.addressLine1 || '',
            addressLine2: parsedEditData?.addressLine2 || '',
            landmark: parsedEditData?.landmark || '',
            location: parsedEditData?.location || '',
            pincode: parsedEditData?.pincode || '',
            state: parsedEditData?.state || '',
            district: parsedEditData?.district || '',
            plotArea: parsedEditData?.plotArea || '',
            price: parsedEditData?.price || '',
            latitude: parsedEditData?.latitude || '',
            longitude: parsedEditData?.longitude || '',
            village: parsedEditData?.village || '',
            surveyNumber: parsedEditData?.surveyNumber || '',
            encOwnerName: parsedEditData?.encOwnerName || '',
            encumbranceDocNumber: parsedEditData?.encumbranceDocNumber || '',
            encumbranceDate: parsedEditData?.encumbranceDate || '',
            encumbranceCert: parsedEditData?.encumbranceCert || null,
            propertyTaxNumber: parsedEditData?.propertyTaxNumber || '',
            propertyTaxDate: parsedEditData?.propertyTaxDate || '',
            propertyTaxRec: parsedEditData?.propertyTaxRec || null,
            siteImages: parsedEditData?.siteImages || [],
        },
        validationSchema: completeSiteSchema,
        onSubmit: async (values) => {
            // Handle form submission
            console.log('Form submitted:', values);
        },
    });

    // Create mutation for site application
    const createMutation = useMutation({
        mutationFn: async (data) => {
            // Placeholder API call
            console.log('Creating site application:', data);
            return Promise.resolve({ success: true });
        },
        onSuccess: () => {
            queryClient.invalidateQueries(['sites']);
            showToast(
                'success',
                'Success',
                'Site application submitted successfully!'
            );
            router.push('/Sites/SiteSuccess');
        },
        onError: (error) => {
            console.error('Create site error:', error);
            showToast(
                'error',
                'Error',
                'Failed to submit site application. Please try again.'
            );
        },
    });

    // Update mutation for site profile
    const updateMutation = useMutation({
        mutationFn: async ({ id, data }) => {
            // Placeholder API call
            console.log('Updating site profile:', id, data);
            return Promise.resolve({ success: true });
        },
        onSuccess: () => {
            queryClient.invalidateQueries(['sites']);
            showToast(
                'success',
                'Success',
                'Site profile updated successfully!'
            );
            router.push('/Sites/SiteSuccess');
        },
        onError: (error) => {
            console.error('Update site error:', error);
            showToast(
                'error',
                'Error',
                'Failed to update site profile. Please try again.'
            );
        },
    });

    const isCreating = createMutation.isPending;
    const isUpdating = updateMutation.isPending;

    // Handle document preview
    const handleDocumentPreview = useCallback((document) => {
        setSelectedDocument(document);
        setShowDocumentPreviewModal(true);
    }, []);

    // Scroll to top when step changes
    useEffect(() => {
        if (scrollViewRef.current) {
            scrollViewRef.current.scrollTo({ y: 0, animated: true });
        }
    }, [step]);

    // Handle form submission
    const handleSubmit = async () => {
        try {
            setIsSubmitting(true);
            const values = formik.values;

            if (isEditing && parsedEditData?.id) {
                await updateMutation.mutateAsync({
                    id: parsedEditData.id,
                    data: values,
                });
            } else {
                await createMutation.mutateAsync(values);
            }
        } catch (error) {
            console.error('Submit error:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    // Render step content
    const renderStepContent = () => {
        const stepProps = {
            theme,
            formik,
            setStep,
            isSubmitting: isSubmitting || isCreating || isUpdating,
            onDocumentPreview: handleDocumentPreview,
            onSubmit: handleSubmit,
        };

        switch (step) {
            case 'userDetails':
                return (
                    <UserDetailsStep
                        theme={theme}
                        user={user}
                        setStep={setStep}
                    />
                );
            case 'siteDetails':
                return <SiteDetailsStep {...stepProps} />;
            case 'location':
                return <LocationStep {...stepProps} />;
            case 'encumbrance':
                return <EncumbranceStep {...stepProps} />;
            case 'propertyTax':
                return <PropertyTaxStep {...stepProps} />;
            case 'review':
                return <ReviewStep {...stepProps} />;
            default:
                return (
                    <UserDetailsStep
                        theme={theme}
                        user={user}
                        setStep={setStep}
                    />
                );
        }
    };

    if (isUserLoading) {
        return (
            <View
                style={[
                    styles.loadingContainer,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <ActivityIndicator size="large" color={theme.PRIMARY} />
                <Text
                    style={[styles.loadingText, { color: theme.TEXT_PRIMARY }]}
                >
                    Loading...
                </Text>
            </View>
        );
    }

    if (userError) {
        return (
            <View
                style={[
                    styles.errorContainer,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    Failed to load user profile. Please try again.
                </Text>
            </View>
        );
    }

    return (
        <>
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                backgroundColor="transparent"
                translucent
            />
            <KeyboardAvoidingView
                style={{ flex: 1 }}
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            >
                <LinearGradient
                    colors={
                        isDarkMode
                            ? ['#1a1a2e', '#16213e', '#0f3460']
                            : ['#667eea', '#764ba2', '#f093fb']
                    }
                    style={styles.container}
                >
                    <Image
                        source={require('../../assets/images/background.png')}
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                    <View style={styles.overlay} />

                    <ScrollView
                        ref={scrollViewRef}
                        contentContainerStyle={styles.scrollContainer}
                        showsVerticalScrollIndicator={false}
                        keyboardShouldPersistTaps="handled"
                    >
                        <BackButton theme={theme} />

                        <View
                            style={[
                                styles.formContainer,
                                {
                                    shadowColor: theme.SHADOW,
                                    backgroundColor: theme.CARD,
                                },
                            ]}
                        >
                            <Text
                                style={[styles.title, { color: theme.PRIMARY }]}
                            >
                                {isEditing
                                    ? 'Edit Site Application'
                                    : 'Site Registration'}
                            </Text>
                            <Text
                                style={[
                                    styles.subtitle,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                {isEditing
                                    ? 'Update your site details'
                                    : 'Register your property for sale'}
                            </Text>
                            <Text
                                style={[
                                    styles.subtitle,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Step{' '}
                                {[
                                    'userDetails',
                                    'siteDetails',
                                    'location',
                                    'encumbrance',
                                    'propertyTax',
                                    'review',
                                ].indexOf(step) + 1}{' '}
                                of 6
                            </Text>

                            {renderStepContent()}
                        </View>
                    </ScrollView>
                </LinearGradient>
            </KeyboardAvoidingView>

            <DocumentPreviewModal
                visible={showDocumentPreviewModal}
                document={selectedDocument}
                onClose={() => setShowDocumentPreviewModal(false)}
                theme={theme}
            />
        </>
    );
};

export default SiteForm;
