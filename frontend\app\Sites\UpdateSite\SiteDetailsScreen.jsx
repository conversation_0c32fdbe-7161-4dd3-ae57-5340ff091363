import React, { useContext } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import InputField from '../InputField';
import FilePicker from '../FilePicker';
import { FormContext, styles } from './SiteFormNavigator';

const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB
const MAX_SITE_IMAGES = 10;

const SiteDetailsScreen = ({ navigation }) => {
  const { fields, setFields } = useContext(FormContext);
  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.h1}>Site Details</Text>
      <View style={styles.card}>
        <Text style={styles.section}>Basic Details</Text>
        <InputField
          label="Site name*"
          value={fields.name}
          onChangeText={(t) => setFields((f) => ({ ...f, name: t }))}
          placeholder="Enter Site name"
        />
        <InputField
          label="Address line 1*"
          value={fields.addressLine1}
          onChangeText={(t) => setFields((f) => ({ ...f, addressLine1: t }))}
          placeholder="Enter Address line 1"
        />
        <InputField
          label="Address line 2"
          value={fields.addressLine2}
          onChangeText={(t) => setFields((f) => ({ ...f, addressLine2: t }))}
          placeholder="Enter Address line 2"
        />
        <InputField
          label="Landmark"
          value={fields.landmark}
          onChangeText={(t) => setFields((f) => ({ ...f, landmark: t }))}
          placeholder="Enter Landmark"
        />
        <InputField
          label="Pincode*"
          value={fields.pincode}
          onChangeText={(t) => setFields((f) => ({ ...f, pincode: t }))}
          keyboardType="numeric"
          placeholder="Enter Pincode"
        />
        <InputField
          label="State"
          value={fields.state}
          onChangeText={(t) => setFields((f) => ({ ...f, state: t }))}
          placeholder="Enter State"
        />
        <InputField
          label="District"
          value={fields.district}
          onChangeText={(t) => setFields((f) => ({ ...f, district: t }))}
          placeholder="Enter District"
        />
        <InputField
          label="Plot area (sqft)*"
          value={fields.plotArea}
          onChangeText={(t) => setFields((f) => ({ ...f, plotArea: t }))}
          keyboardType="numeric"
          placeholder="Enter Plot area"
        />
        <InputField
          label="Price"
          value={fields.price}
          onChangeText={(t) => setFields((f) => ({ ...f, price: t }))}
          keyboardType="numeric"
          placeholder="Enter Price"
        />
        <FilePicker
          label={`Pick site image${fields.siteImages?.length > 0 ? 's' : ''} (JPG/PNG/PDF, max ${MAX_SITE_IMAGES}`}
          files={fields.siteImages}
          setFiles={setFields}
          keyName="siteImages"
          maxFiles={MAX_SITE_IMAGES}
          allowedTypes={ALLOWED_TYPES}
          maxFileSize={MAX_FILE_SIZE}
          isMultiple={true}
        />
      </View>
      <TouchableOpacity
        style={styles.nextButton}
        onPress={() => navigation.navigate('Location')}
        activeOpacity={0.8}
      >
        <Text style={styles.nextButtonText}>Next</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

export default SiteDetailsScreen;