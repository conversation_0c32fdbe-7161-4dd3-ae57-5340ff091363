import React from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { styles } from './styles';
import FilePicker from './FilePicker';

const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB
const MAX_SITE_IMAGES = 10;

const SiteDetailsStep = ({ theme, formik, setStep, isSubmitting, onDocumentPreview }) => {
    const { values, errors, touched, handleChange, handleBlur, setFieldValue, validateForm, setFieldTouched } = formik;

    const handleNext = async () => {
        const errors = await validateForm();
        const fields = [
            'name',
            'addressLine1',
            'pincode',
            'state',
            'district',
            'plotArea',
            'siteImages',
        ];
        if (!fields.some((field) => errors[field])) {
            setStep('location');
        } else {
            fields.forEach((field) =>
                setFieldTouched(field, true)
            );
        }
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Site Details
            </Text>
            <Text style={[styles.subtitle, { color: theme.TEXT_SECONDARY }]}>
                Provide basic information about your property
            </Text>

            {/* Site Name */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.name && touched.name
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="home-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.name}
                    onChangeText={handleChange('name')}
                    onBlur={handleBlur('name')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter site name"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="Site name"
                />
            </View>
            {errors.name && touched.name && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.name}
                </Text>
            )}

            {/* Address Line 1 */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.addressLine1 && touched.addressLine1
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="location-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.addressLine1}
                    onChangeText={handleChange('addressLine1')}
                    onBlur={handleBlur('addressLine1')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter address line 1"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="Address line 1"
                />
            </View>
            {errors.addressLine1 && touched.addressLine1 && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.addressLine1}
                </Text>
            )}

            {/* Address Line 2 */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="location-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.addressLine2}
                    onChangeText={handleChange('addressLine2')}
                    onBlur={handleBlur('addressLine2')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter address line 2 (optional)"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="Address line 2"
                />
            </View>

            {/* Landmark */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="flag-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.landmark}
                    onChangeText={handleChange('landmark')}
                    onBlur={handleBlur('landmark')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter landmark (optional)"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="Landmark"
                />
            </View>

            {/* Pincode */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.pincode && touched.pincode
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="mail-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.pincode}
                    onChangeText={handleChange('pincode')}
                    onBlur={handleBlur('pincode')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter pincode"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    keyboardType="numeric"
                    accessibilityLabel="Pincode"
                />
            </View>
            {errors.pincode && touched.pincode && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.pincode}
                </Text>
            )}

            {/* State */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.state && touched.state
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="map-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.state}
                    onChangeText={handleChange('state')}
                    onBlur={handleBlur('state')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter state"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="State"
                />
            </View>
            {errors.state && touched.state && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.state}
                </Text>
            )}

            {/* District */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.district && touched.district
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="business-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.district}
                    onChangeText={handleChange('district')}
                    onBlur={handleBlur('district')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter district"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    accessibilityLabel="District"
                />
            </View>
            {errors.district && touched.district && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.district}
                </Text>
            )}

            {/* Plot Area */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: errors.plotArea && touched.plotArea
                            ? theme.ERROR
                            : theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="resize-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.plotArea}
                    onChangeText={handleChange('plotArea')}
                    onBlur={handleBlur('plotArea')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter plot area (sqft)"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    keyboardType="numeric"
                    accessibilityLabel="Plot area"
                />
            </View>
            {errors.plotArea && touched.plotArea && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.plotArea}
                </Text>
            )}

            {/* Price */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="cash-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    value={values.price}
                    onChangeText={handleChange('price')}
                    onBlur={handleBlur('price')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholder="Enter price (optional)"
                    placeholderTextColor={theme.TEXT_SECONDARY}
                    keyboardType="numeric"
                    accessibilityLabel="Price"
                />
            </View>

            {/* Site Images */}
            <FilePicker
                label={`Site Images (JPG/PNG/PDF, max ${MAX_SITE_IMAGES})`}
                files={values.siteImages}
                setFiles={(files) => setFieldValue('siteImages', files)}
                keyName="siteImages"
                maxFiles={MAX_SITE_IMAGES}
                allowedTypes={ALLOWED_TYPES}
                maxFileSize={MAX_FILE_SIZE}
                isMultiple={true}
                theme={theme}
                onDocumentPreview={onDocumentPreview}
            />
            {errors.siteImages && touched.siteImages && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.siteImages}
                </Text>
            )}

            {/* Next Button */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={styles.submitButton}
                    onPress={handleNext}
                    disabled={isSubmitting}
                    accessibilityLabel="Proceed to location details"
                    accessibilityRole="button"
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.submitButtonGradient}
                    >
                        <Text
                            style={[
                                styles.submitButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            Next
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default SiteDetailsStep;
