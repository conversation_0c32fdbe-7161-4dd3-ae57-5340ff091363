'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

export default function BrokersPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-foreground">Site Scouts</h1>
        <p className="mt-2 text-muted-foreground">
          Connect with verified site scouts to find your perfect property
        </p>
      </div>

      {/* Search and Filters */}
      <div className="mb-8 flex flex-col gap-4 sm:flex-row">
        <div className="flex-1">
          <Input placeholder="Search by name, location, or specialization..." />
        </div>
        <Button variant="outline">Filters</Button>
        <Button>Search</Button>
      </div>

      {/* Broker Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Sample Broker Cards */}
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="group cursor-pointer transition-all hover:shadow-card-hover">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 h-20 w-20 rounded-full bg-muted flex items-center justify-center">
                <span className="text-2xl">👤</span>
              </div>
              <CardTitle className="text-lg">Rajesh Kumar</CardTitle>
              <CardDescription>Site Scout • 5+ years experience</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Rating:</span>
                  <span className="font-medium">4.8 ⭐ (156 reviews)</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Service Areas:</span>
                  <span className="font-medium">Bangalore, Mysore</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Properties Sold:</span>
                  <span className="font-medium">45+</span>
                </div>
                <Button className="w-full" size="sm">
                  Contact Scout
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More */}
      <div className="mt-12 text-center">
        <Button variant="outline" size="lg">
          Load More Site Scouts
        </Button>
      </div>
    </div>
  )
}
