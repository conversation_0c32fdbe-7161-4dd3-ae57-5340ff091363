import React, { useContext } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import InputField from '../InputField';
import FilePicker from '../FilePicker';
import { FormContext, styles } from './SiteFormNavigator';

const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB

const PropertyTaxScreen = ({ navigation }) => {
  const { fields, setFields } = useContext(FormContext);
  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.h1}>Property Tax Receipt</Text>
      <View style={styles.card}>
        <Text style={styles.section}>Property Tax Receipt</Text>
        <InputField
          label="Owner name"
          value={fields.ptrOwnerName}
          onChangeText={(t) => setFields((f) => ({ ...f, ptrOwnerName: t }))}
          placeholder="Enter Owner name"
        />
        <InputField
          label="Receipt No."
          value={fields.ptrReciptNo}
          onChangeText={(t) => setFields((f) => ({ ...f, ptrReciptNo: t }))}
          placeholder="Enter Receipt No."
        />
        <FilePicker
          label="Pick property tax receipt"
          files={fields.propertyTaxRec}
          setFiles={setFields}
          keyName="propertyTaxRec"
          allowedTypes={ALLOWED_TYPES}
          maxFileSize={MAX_FILE_SIZE}
        />
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.8}
        >
          <Text style={styles.backButtonText}>Back</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.nextButton}
          onPress={() => navigation.navigate('Submit')}
          activeOpacity={0.8}
        >
          <Text style={styles.nextButtonText}>Next</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

export default PropertyTaxScreen;