// Color palette based on mobile app
export const COLORS = {
  LIGHT: {
    WHITE: '#FFFFFF',
    BLACK: '#000000',
    BACKGROUND: '#F5F7FA',
    CARD: '#FFFFFF',
    PRIMARY: '#2A8E9E',
    SECONDARY: '#001d3d',
    GRADIENT_PRIMARY: '#2A8E9E',
    GRADIENT_SECONDARY: '#001d3d',
    LOGO_BORDER: 'rgba(255,255,255,0.3)',
    SHADOW: '#000000',
    INPUT_BACKGROUND: '#f9f9f9',
    INPUT_BORDER: '#e0e0e0',
    ACCENT: '#E6F3F7',
    GRAY: '#9CA3AF',
    GRAY_LIGHT: '#E0E0E0',
    TEXT_PRIMARY: '#212121',
    TEXT_SECONDARY: '#757575',
    TEXT_PLACEHOLDER: '#666',
    SUCCESS: '#4CAF50',
    INFO: '#87CEFA',
    WARNING: '#FFB300',
    ERROR: '#D32F2F',
    TOAST_BLACK: 'rgba(60,60,60,0.9)',
  },
  DARK: {
    WHITE: '#FFFFFF',
    BLACK: '#000000',
    BACKGROUND: '#121212',
    CARD: '#1E1E1E',
    PRIMARY: '#2A8E9E',
    SECONDARY: '#001d3d',
    GRADIENT_PRIMARY: 'rgba(42, 142, 158, 0.7)',
    GRADIENT_SECONDARY: '#B0B0B0',
    LOGO_BORDER: 'rgba(255,255,255,0.4)',
    SHADOW: '#000000',
    INPUT_BACKGROUND: '#1E1E1E',
    INPUT_BORDER: '#333333',
    ACCENT: '#1B2A33',
    GRAY: '#9CA3AF',
    GRAY_LIGHT: '#B0B0B0',
    TEXT_PRIMARY: '#E0E0E0',
    TEXT_SECONDARY: '#B0B0B0',
    TEXT_PLACEHOLDER: '#666',
    SUCCESS: '#4CAF50',
    INFO: '#87CEFA',
    WARNING: '#FFB300',
    ERROR: '#D32F2F',
    TOAST_BLACK: 'rgba(60,60,60,0.9)',
  },
} as const

// Quick access navigation items from mobile app
export const QUICK_ACCESS_ITEMS = [
  {
    id: 1,
    name: 'Properties',
    icon: 'home-map-marker',
    route: '/properties',
    description: 'Browse and manage properties'
  },
  {
    id: 2,
    name: 'Contractors',
    icon: 'engineering',
    route: '/contractors',
    description: 'Find verified contractors'
  },
  {
    id: 3,
    name: 'Site Scouts',
    icon: 'person-search',
    route: '/brokers',
    description: 'Connect with site scouts'
  },
  {
    id: 4,
    name: 'Map Explorer',
    icon: 'map',
    route: '/map',
    description: 'Explore properties on map'
  },
  {
    id: 5,
    name: 'Chats',
    icon: 'chatbubbles-outline',
    route: '/chats',
    description: 'Your conversations'
  },
  {
    id: 6,
    name: 'Support',
    icon: 'support-agent',
    route: '/support',
    description: 'Get help and support'
  },
] as const

// User roles
export const USER_ROLES = {
  BUYER: 'buyer',
  BROKER: 'broker',
  CONTRACTOR: 'contractor',
  ADMIN: 'admin',
} as const

// Property types
export const PROPERTY_TYPES = {
  RESIDENTIAL: 'residential',
  COMMERCIAL: 'commercial',
  AGRICULTURAL: 'agricultural',
  INDUSTRIAL: 'industrial',
} as const

// Application status
export const APPLICATION_STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  UNDER_REVIEW: 'under_review',
} as const

// Chat message types
export const MESSAGE_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  FILE: 'file',
  LOCATION: 'location',
} as const

// API endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    ME: '/api/auth/me',
  },
  PROPERTIES: {
    LIST: '/api/properties',
    CREATE: '/api/properties',
    GET: (id: string) => `/api/properties/${id}`,
    UPDATE: (id: string) => `/api/properties/${id}`,
    DELETE: (id: string) => `/api/properties/${id}`,
  },
  BROKERS: {
    LIST: '/api/brokers',
    APPLICATION: '/api/brokers/application',
    GET: (id: string) => `/api/brokers/${id}`,
  },
  CONTRACTORS: {
    LIST: '/api/contractors',
    APPLICATION: '/api/contractors/application',
    GET: (id: string) => `/api/contractors/${id}`,
  },
  CHAT: {
    ROOMS: '/api/chat/rooms',
    MESSAGES: (roomId: string) => `/api/chat/rooms/${roomId}/messages`,
  },
  MAPS: {
    PROPERTIES: '/api/maps/properties',
    SEARCH: '/api/maps/search',
  },
} as const

// Validation constants
export const VALIDATION = {
  PASSWORD_MIN_LENGTH: 8,
  FILE_MAX_SIZE: 5 * 1024 * 1024, // 5MB
  IMAGE_MAX_SIZE: 2 * 1024 * 1024, // 2MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'image/jpeg', 'image/png'],
} as const
