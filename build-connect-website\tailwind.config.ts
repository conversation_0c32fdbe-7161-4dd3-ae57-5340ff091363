import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: ["class"],
  theme: {
    extend: {
      colors: {
        // Light theme colors based on mobile app
        background: '#F5F7FA',
        foreground: '#212121',
        card: '#FFFFFF',
        'card-foreground': '#212121',
        primary: {
          DEFAULT: '#2A8E9E',
          foreground: '#FFFFFF',
        },
        secondary: {
          DEFAULT: '#001d3d',
          foreground: '#FFFFFF',
        },
        accent: {
          DEFAULT: '#E6F3F7',
          foreground: '#212121',
        },
        muted: {
          DEFAULT: '#f9f9f9',
          foreground: '#757575',
        },
        destructive: {
          DEFAULT: '#D32F2F',
          foreground: '#FFFFFF',
        },
        border: '#e0e0e0',
        input: '#f9f9f9',
        ring: '#2A8E9E',
        success: '#4CAF50',
        warning: '#FFB300',
        info: '#87CEFA',
        'text-secondary': '#757575',
        'gray-light': '#E0E0E0',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        display: ['Inter', 'system-ui', 'sans-serif'],
      },
      boxShadow: {
        'fab': '0px 2px 10px rgba(0, 0, 0, 0.25)',
        'card': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'card-hover': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      },
      borderRadius: {
        lg: '12px',
        md: '8px',
        sm: '6px',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
};

export default config;
