'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

export default function ContractorsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-foreground">Contractors</h1>
        <p className="mt-2 text-muted-foreground">
          Find skilled contractors for your construction and renovation needs
        </p>
      </div>

      {/* Search and Filters */}
      <div className="mb-8 flex flex-col gap-4 sm:flex-row">
        <div className="flex-1">
          <Input placeholder="Search by name, specialization, or location..." />
        </div>
        <Button variant="outline">Filters</Button>
        <Button>Search</Button>
      </div>

      {/* Contractor Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Sample Contractor Cards */}
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="group cursor-pointer transition-all hover:shadow-card-hover">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 h-20 w-20 rounded-full bg-muted flex items-center justify-center">
                <span className="text-2xl">👷</span>
              </div>
              <CardTitle className="text-lg">Suresh Construction</CardTitle>
              <CardDescription>General Contractor • 8+ years experience</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Rating:</span>
                  <span className="font-medium">4.9 ⭐ (89 reviews)</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Specialization:</span>
                  <span className="font-medium">Residential, Commercial</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Projects Done:</span>
                  <span className="font-medium">120+</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Service Areas:</span>
                  <span className="font-medium">Bangalore, Mysore</span>
                </div>
                <Button className="w-full" size="sm">
                  Contact Contractor
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More */}
      <div className="mt-12 text-center">
        <Button variant="outline" size="lg">
          Load More Contractors
        </Button>
      </div>
    </div>
  )
}
